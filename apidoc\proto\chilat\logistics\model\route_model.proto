syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics.model";

import "common.proto";

message RoutePageListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated RouteModel data = 3;
}

message RouteModel {
  string id = 1;
  string routeName = 2;
  string routeCode = 3;
  string routeAlias = 4;
  string remark = 5;
  bool enabled = 6;
  repeated RouteTransportMethodModel transportMethods = 7;
  repeated RouteTransportDaysModel transportDays = 8;
  string coperator = 9;
  int64 cdate = 10;
}

message RouteTransportMethodModel {
  string id = 1;
  string transportMethodCode = 2;
  string transportMethodName = 3;
}

message RouteTransportDaysModel {
  string countryId = 10; //国家id
  string countryName = 20; //国家名称
  string minDays = 30; //最快时效
  string maxDays = 44; //最慢时效
  double m3FreightUsd = 50; //1立方米预估运费，单位：美元
  double kgFreightUsd = 60; //1公斤（KG）预估运费，单位：美元
}

message RouteLogListResp {
  common.Result result = 1;
  repeated common.LogModel data = 2;
}

message RouteListResp {
  common.Result result = 1;
  repeated RouteModel data = 2;
}

message CountryRouteListResp {
  common.Result result = 1;
  repeated CountryRouteModel data = 2;
}

message CountryRouteModel {
  string id = 1;
  string routeName = 2;
  string routeCode = 3;
  string routeAlias = 4;
  string remark = 5;
  bool enabled = 6;
  RouteTransportDaysModel transportDays = 8;
  string coperator = 9;
  int64 cdate = 10;
}

