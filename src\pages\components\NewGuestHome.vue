<template>
  <div class="page-wrapper">
    <n-marquee class="bg-black">
      <div
        class="h-[50px] leading-[50px] flex items-center gap-[10px] text-[14px] leading-[14px]"
      >
        <img
          src="@/assets/icons/home/<USER>"
          alt=""
          class="w-[22px] h-[22px]"
        />
        <span class="text-[#FFB700] font-medium"> <PERSON><PERSON><PERSON>de US$ 2,000 </span>
        <span class="text-[#7F7F7F]">hasta contenedor cerrado</span>
        <span class="text-[#D9D9D9]"
          >hace compras directamente del mercado mayorista chino</span
        >
      </div>
    </n-marquee>
    <div class="header-wrapper px-[38px] py-[20px] text-white">
      <div class="header-content">
        <div class="flex items-center">
          <img
            src="@/assets/icons/common/logo-white.svg"
            alt="ChilatShop"
            class="w-[218px] mr-auto"
          />
          <div class="country-select flex items-center mr-[60px]">
            <img
              loading="lazy"
              alt="address"
              class="w-[12px] mr-[8px]"
              src="@/assets/icons/common/address.svg"
            />
            <CountrySelect
              mode="popover"
              @save="onSaveCountry"
              spm="select_site_from_nav"
            />
          </div>
          <div class="flex gap-[20px]">
            <a
              class="hover:underline cursor-pointer"
              v-for="(news, index) in newsData"
              :key="index"
              :href="news.path"
              target="_blank"
            >
              {{ news.title }}
            </a>
          </div>
        </div>
      </div>
      <div class="text-[94px] leading-[94px] font-medium text-white mt-[72px]">
        <span class="text-[#E50113]">Chilat</span>Shop
      </div>
      <div class="mt-[40px] flex gap-[72px]">
        <div class="w-[446px] text-[20px] leading-[30px]">
          Plataforma online de compra al por mayor diseñada para importadores
          latinoamericanos.
        </div>
        <div class="flex gap-[16px]">
          <div
            @click="onLoginClick('top', 1)"
            class="w-[222px] h-[60px] flex justify-center items-center border-1 border-[#fff] rounded-[500px] text-[18px] leading-[18px] cursor-pointer hover:bg-white hover:text-[#E50113] hover:font-medium transition-all duration-300"
          >
            {{ authStore.i18n("cm_common_login") }}
          </div>
          <div
            class="relative w-[222px] h-[60px] flex justify-center items-center bg-[#E50113] rounded-[500px] text-[18px] leading-[18px] cursor-pointer hover:bg-[#F20114] hover:font-medium transition-all duration-300"
            @click="onLoginClick('top', 0)"
          >
            {{ authStore.i18n("cm_common_register") }}
            <img
              loading="lazy"
              class="absolute z-10 w-[70px] right-[-20px] top-[-40px]"
              src="@/assets/icons/gift.webp"
              referrerpolicy="no-referrer"
            />
          </div>
        </div>
      </div>
      <div class="text-[16px] leading-[22px] flex mt-[82px] opacity-80">
        <div class="w-[358px]">
          Reúne millones de productos directos de fábrica, generación de lista
          de selección online, control de calidad estandarizado y garantía de
          pago.
        </div>
        <div class="w-[1px] h-[40px] ml-[32px] mr-[33px] bg-white"></div>
        <div class="w-[358px]">
          Permite consolidar pedidos de múltiples proveedores y acompañamiento
          en logística internacional.
        </div>
        <div class="w-[1px] h-[40px] ml-[32px] mr-[33px] bg-white"></div>
        <div class="w-[358px]">
          Te permite importar desde China en volumen de forma tan sencilla como
          comprar en línea.
        </div>
      </div>
    </div>

    <!-- 福利明细 -->
    <div
      class="benefit-breakdown relative w-[1240px] h-[290px] mx-auto bg-[#ac2023] rounded-[12px] mt-[-58px]"
    >
      <img src="@/assets/icons/home/<USER>" alt="" class="absolute" />
      <div
        class="pt-[36px] px-[44px] flex justify-between relative z-2 text-white"
      >
        <div
          v-for="(item, index) in benefitBreakdown"
          :key="index"
          class="flex justify-between gap-[19px]"
        >
          <div class="w-[200px]">
            <img :src="item.icon" :alt="item.title" class="w-[32px] h-[32px]" />
            <div
              class="text-[20px] leading-[24px] font-medium mt-[20px] mb-[16px] h-[48px]"
            >
              {{ item.title }}
            </div>
            <div class="text-[16px] leading-[20px]">{{ item.desc }}</div>
          </div>
          <div
            class="w-[1px] h-[48px] bg-white mt-[48px]"
            v-show="index !== benefitBreakdown.length - 1"
          ></div>
        </div>
      </div>
    </div>

    <div class="w-full px-[64px] pt-[140px]">
      <div class="flex flex-col items-center gap-[40px]">
        <div
          class="relative w-[80px] h-[80px] flex items-center justify-center"
        >
          <img
            src="@/assets/icons/home/<USER>"
            alt="3 pasos para comprar, simple y eficiente"
            class="w-[80px] h-[80px] absolute top-0"
          />
          <div
            class="text-[50px] leading-[50px] text-center text-[#e50113] relative z-2"
          >
            {{ buySteps.length }}
          </div>
        </div>
        <div class="text-[34px] leading-[34px] font-medium">
          3 pasos para comprar, simple y eficiente
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113]"></div>
      </div>

      <div class="flex mt-[78px]">
        <!-- 左侧主图区域 -->
        <div class="w-[508px] flex flex-col space-y-[64px]">
          <div
            v-for="(step, index) in buySteps"
            :key="index"
            class="relative w-full h-[200px]"
          >
            <img
              :src="step.image"
              :alt="`步骤 ${index + 1}`"
              class="w-full h-[200px] object-cover rounded-[12px]"
              loading="lazy"
            />
            <!-- 蒙层效果 -->
            <div
              v-if="index !== currentStep"
              class="absolute inset-0 bg-white bg-opacity-60 rounded-[12px] transition-opacity duration-300"
            ></div>
          </div>
        </div>

        <!-- 中间步骤线区域 -->
        <div
          class="w-[50px] relative flex flex-col items-center min-h-full ml-[58px] mr-[28px]"
        >
          <!-- 背景线 -->
          <div
            class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#F2F2F2]"
            :style="{
              top: getStepPosition(0) + 25 + 'px',
              zIndex: 1,
              height: backgroundLineHeight + 'px',
            }"
          ></div>
          <!-- 进度线 -->
          <div
            class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#E50113] transition-all duration-500 ease-out"
            :style="{
              top: getStepPosition(0) + 25 + 'px',
              height: progressLineHeight + 'px',
              zIndex: 2,
            }"
          ></div>

          <!-- 步骤点 -->
          <div
            v-for="(step, index) in buySteps"
            :key="index"
            :ref="(el) => (stepRefs[index] = el)"
            class="absolute"
            :style="{
              top: getStepPosition(index) + 'px',
              zIndex: 10,
            }"
          >
            <div class="relative w-[50px] h-[50px] z-20 bg-white rounded-full">
              <!-- SVG 进度圆圈 -->
              <svg
                class="absolute inset-0 w-[50px] h-[50px] transform -rotate-90"
                viewBox="0 0 50 50"
              >
                <!-- 背景圆圈 -->
                <circle
                  cx="25"
                  cy="25"
                  r="24"
                  fill="none"
                  stroke="#e5e5e5"
                  stroke-width="1"
                />
                <!-- 进度圆圈 -->
                <circle
                  cx="25"
                  cy="25"
                  r="24"
                  fill="none"
                  :stroke="index <= currentStep ? '#e50113' : '#e5e5e5'"
                  stroke-width="1"
                  stroke-dasharray="150.8"
                  :stroke-dashoffset="index <= currentStep ? 0 : 150.8"
                  class="transition-all duration-500 ease-out"
                  :class="{
                    'step-circle-animate':
                      index === currentStep && stepAnimations[index],
                  }"
                />
              </svg>

              <!-- 步骤数字 -->
              <div
                class="absolute inset-0 flex items-center justify-center text-[34px] leading-[34px] transition-colors duration-500"
                :class="
                  index === currentStep ? 'text-[#e50113]' : 'text-[#999]'
                "
              >
                {{ index + 1 }}
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex-1 flex flex-col">
          <div
            v-for="(step, index) in buySteps"
            :key="index"
            :ref="(el) => (contentRefs[index] = el)"
            class="step-content mb-[64px]"
            :class="{ active: index === currentStep }"
          >
            <div
              class="text-[20px] leading-[20px] font-medium py-[24px] border-b-[1px] border-[#F2F2F2] mb-[34px]"
              :class="index > 0 ? 'border-t-[1px]' : ''"
            >
              {{ step.title }}
            </div>
            <div class="space-y-[30px]">
              <div
                v-for="(item, itemIndex) in step.desc"
                :key="itemIndex"
                class="flex items-center gap-[10px]"
              >
                <img
                  loading="lazy"
                  class="w-[14px]"
                  alt="check"
                  src="@/assets/icons/home/<USER>"
                />

                <span class="text-[16px] leading-[16px] text-[#7F7F7F]">{{
                  item
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="min-w-[1280px] pt-[80px]">
      <!-- 热销货盘 -->
      <HotPallets
        :selectorList="props.selectorList"
        :recommendSupplierGoods="props.recommendSupplierGoods"
        :habitableCapsuleGoods="props.habitableCapsuleGoods"
        class="w-[1200px] mx-auto"
      ></HotPallets>
    </div>

    <!-- 降低成本，增加效益 -->
    <div class="simplify-purchase">
      <div class="w-[1200px] mx-auto flex items-center">
        <div class="basis-2/3 mr-10">
          <div class="purchase-title">
            {{ authStore.i18n("cm_guestHome.purchaseTitle") }}
          </div>
          <div class="purchase-desc">
            {{ authStore.i18n("cm_guestHome.purchaseDesc") }}
          </div>
        </div>
        <img
          loading="lazy"
          :src="purchaseImg"
          class="w-[360px]"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>

    <!-- 商品分类 -->
    <div class="category-wrapper" data-spm-box="homepage-top-categories">
      <div class="w-[1200px] mx-auto">
        <div class="h-52 flex justify-between">
          <div class="category-title">
            {{ authStore.i18n("cm_guestHome.categoryTitle") }}
          </div>
          <div class="category-number">
            <div
              class="number-item"
              v-for="(item, index) in cateIntroData"
              :key="index"
            >
              <span>{{ item.number }}</span>
              <p>
                {{ item.title }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全 -->
    <div class="security-floor" :style="`background-image: url(${securityBg})`">
      <div class="security-floor_wrapper">
        <div class="security-floor_title">
          {{ authStore.i18n("cm_guestHome.securityTitle") }}
        </div>
        <div class="flex justify-between">
          <div
            class="security-floor_card"
            v-for="(item, index) in securityData"
            :key="index"
          >
            <div class="flex items-center">
              <n-image
                lazy
                preview-disabled
                class="security-floor_icon"
                :src="item.icon"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              />
              <div class="font-medium text-2xl">
                {{ item.title }}
              </div>
            </div>
            <div class="security-floor_card_text_2">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 为什么选择我们？ -->
    <div class="full-link">
      <div class="full-link_box">
        <ul class="full-link_item_wrapper">
          <div class="full-title">
            {{ authStore.i18n("cm_guestHome.fullTitle") }}
          </div>
          <li
            class="full-link_item"
            v-for="(link, index) in chooseData"
            :key="index"
          >
            <!-- <div class="full-link_icon"></div> -->
            <div class="full-link_content">
              <div class="full-link_item_title">
                <span class="text-[#e50113] text-[32px]">0{{ index + 1 }}</span>
                {{ link.title }}
              </div>
              <div class="full-link_item_desc">
                {{ link.desc }}
              </div>
            </div>
          </li>

          <!-- <div class="full-link_item_tail"></div> -->
        </ul>
        <div class="h-[300px]">
          <n-carousel
            autoplay
            show-dots
            style="height: 300px"
            :transition-style="{ transitionDuration: '500ms' }"
            :interval="3000"
            class="full-link_carousel"
          >
            <n-image
              lazy
              preview-disabled
              :src="carousel"
              v-for="(carousel, index) in carouselData"
              :key="index"
              class="h-[300px]"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
          </n-carousel>
        </div>
      </div>
    </div>

    <!-- 注册登录 -->
    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">
          {{ authStore.i18n("cm_guestHome.loginTitle") }}
        </div>
        <div class="login-desc">
          {{ authStore.i18n("cm_guestHome.loginDesc") }}
        </div>
        <n-button
          color="#e50113"
          class="section_banner-button"
          @click="onLoginClick('', 0)"
        >
          {{ authStore.i18n("cm_common_registerNow") }}
        </n-button>
      </div>
    </div>

    <!-- 客户评价 -->
    <div class="user-video">
      <div class="user-video-wrapper">
        <div class="video-title">
          {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
        </div>
        <div class="flex justify-between">
          <div
            class="video-wrapper"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video, index)"
          >
            <n-image
              lazy
              preview-disabled
              :src="video.videoBg"
              class="img"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
            <div class="video-icon">
              <IconCard
                name="mingcute:play-fill"
                size="20"
                color="#322623"
              ></IconCard>
            </div>
          </div>
        </div>
      </div>
    </div>
    <PageFooter></PageFooter>
  </div>
  <VideoModal ref="videoModalRef"></VideoModal>
  <LoginRegisterModal ref="loginRegisterModalRef"></LoginRegisterModal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import { useHomePageCategory } from "@/composables/http";
import { navigateToPage } from "@/utils/mixin";
import { goodsListAllPath } from "@/utils/constant";
import LoginRegisterModal from "./LoginRegisterModal.vue";
import HotPallets from "./HotSelectors.vue";
import CountrySelect from "@/components/CountrySelect.vue";
import PageFooter from "@/components/PageFooter.vue";
import VideoModal from "@/components/videoModal.vue";
import IconCard from "@/components/IconCard.vue";

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");

interface selectorItem {
  spmCode: string;
  name: string;
  tagId: string;
  goodsList: any[];
}

const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
  hotKeywords: {
    type: Array,
    default: () => [],
  },
  selectorList: {
    type: Array as () => selectorItem[],
    default: () => [],
  },
  recommendSupplierGoods: {
    type: Object,
    default: () => <any>{},
  },
  habitableCapsuleGoods: {
    type: Object,
    default: () => <any>{},
  },
});

const pageData = reactive(<any>{
  slidDis: 0,
  showNextArrow: true,
  showPrevArrow: false,
  categoryData: <any>[],
  categoryInRow: 7,
  categoryAllWidth: 0, //所有分类的宽度
  categoryItemWidth: 175, // 分类子项目的宽度
  categoryContainerWidth: 0, //分类容器的宽度
  activatedLinkIndex: 0,
  isHeaderFixed: false,
  isHeaderButtonFixed: false,
  showKwCarousel: true, //是否展示轮播关键词
  mercadoArrow: false,
  keyword: "",
});

const videoModalRef = ref<any>(null);
const pageTheme = computed(() => useConfigStore().getPageTheme);

import headerBg from "@/assets/icons/home/<USER>";
import whiteLogo from "@/assets/icons/white-logo.svg";
import factoryDirect from "@/assets/icons/home/<USER>";
import quickQuote from "@/assets/icons/home/<USER>";
import cargoConsolidation from "@/assets/icons/home/<USER>";
import totalControl from "@/assets/icons/home/<USER>";
import hassleFreeShipping from "@/assets/icons/home/<USER>";
import selectionQuotation from "@/assets/icons/home/<USER>";
import confirmationPayment from "@/assets/icons/home/<USER>";
import qualityControlShipping from "@/assets/icons/home/<USER>";

// import headerBg from "@/assets/icons/guestHome.jpg";
const arrowIcon =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/24/47431a43-32e6-4442-8da5-4ef1609e966e.png";
const purchaseImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/e7a794cc-524c-485c-bc14-50932983530c.png";
const securityBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/158870e0-a43f-40e7-a53a-2cf4fad3c3b7.jpg";
const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/6d5472a2-a993-481f-ad19-9ab151d8e7ae.jpg";

const newsData = [
  {
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    title: authStore.i18n("cm_news.helpCenter"),
    path: "/article/help-center",
  },
  {
    title: authStore.i18n("cm_news.commission"),
    path: "/article/commission",
  },
  {
    title: authStore.i18n("cm_news.chilatshopTutorials"),
    path: "/article/tutorials",
  },
];

const benefitBreakdown = [
  {
    icon: factoryDirect,
    title: "Directo desde fábrica",
    desc: "Productos a precios competitivos sin intermediarios.",
  },
  {
    icon: quickQuote,
    title: "Cotización rápida",
    desc: "Generas tu lista de pedido online y te damos precios inmediatos.",
  },
  {
    icon: cargoConsolidation,
    title: "Consolidación de carga",
    desc: "Compras a varios proveedores y enviamos todo en un mismo contenedor.",
  },
  {
    icon: totalControl,
    title: "Cotización rápida",
    desc: "Verificamos calidad y gestionamos el pago por ti.",
  },
  {
    icon: hassleFreeShipping,
    title: "Cotización rápida",
    desc: "Asistimos en la logística internacional hasta tu país.",
  },
];

const cateIntroData = [
  {
    number: authStore.i18n("cm_guestHome.number1"),
    title: authStore.i18n("cm_guestHome.numberDesc1"),
  },
  {
    number: authStore.i18n("cm_guestHome.number2"),
    title: authStore.i18n("cm_guestHome.numberDesc2"),
  },
];

const securityData = [
  {
    title: authStore.i18n("cm_guestHome.securityQual"),
    desc: authStore.i18n("cm_guestHome.securityQualDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3447a23e-9bb7-4824-a978-97ee506721b8.png",
  },
  {
    title: authStore.i18n("cm_guestHome.securityPay"),
    desc: authStore.i18n("cm_guestHome.securityPayDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/001a3c9b-c5b1-42be-8eb9-ca6eb718d918.png",
  },
];

const chooseData = [
  {
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const carouselData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

async function onScroll() {
  let scrollTop =
    window.pageYOffset ||
    document.documentElement.scrollTop ||
    document.body.scrollTop;

  const pageHeader = document.querySelector("#page-header") as HTMLElement;
  const headerHeight = pageHeader?.offsetHeight;
  const pageSearchButton =
    document.querySelector("#page-header-button") || <any>{};
  // 判断是否显示固定搜索
  if (scrollTop > 0) {
    pageData.isHeaderFixed = true;
  } else {
    pageData.isHeaderFixed = false;
  }

  if (
    scrollTop >
    pageSearchButton.offsetTop + pageSearchButton.offsetHeight / 2
  ) {
    pageData.isHeaderButtonFixed = true;
  } else {
    pageData.isHeaderButtonFixed = false;
  }
}

function nextSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  ); // 向上取整
  const restItems = pageData.categoryData.length - slidItems;
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, restItems); // 最多滑动5个项目或少于5个

  if (restItems > 0) {
    pageData.slidDis -= pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动后已经到达或超过了总宽度，则隐藏“下一箭头”
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.slidDis = -(
        pageData.categoryAllWidth - pageData.categoryContainerWidth
      ); // 保证不会超出边界
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showNextArrow = false; // 没有剩余项目，隐藏下一箭头
  }

  // 始终显示上一箭头
  pageData.showPrevArrow = true;
}

function prevSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  );
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, slidItems);
  if (slidItems > 0) {
    pageData.slidDis += pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动回到第一屏，隐藏“上一箭头”
    if (pageData.slidDis >= 0) {
      pageData.slidDis = 0; // 确保不会超出边界
      pageData.showPrevArrow = false;
    } else {
      pageData.showPrevArrow = true;
    }

    // 始终显示“下一箭头”，除非已经滑到最末尾
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showPrevArrow = false;
  }
}

onPageCategoryData();
async function onPageCategoryData() {
  const res: any = await useHomePageCategory({});
  if (res?.result?.code === 200) {
    pageData.categoryData = organizeDataByColumns(res?.data);
    pageData.showNextArrow =
      pageData.categoryData.length > pageData.categoryInRow ? true : false;
    nextTick(() => {
      // 分类子项目的宽度
      const element = document?.querySelector(".category-row a");
      if (!element) return;
      pageData.categoryItemWidth = element.getBoundingClientRect().width || 175;
      pageData.categoryContainerWidth = (
        document?.querySelector(".slider_inner") as HTMLElement
      )?.offsetWidth; // 分类容器的宽度
      pageData.categoryAllWidth =
        pageData.categoryData.length * pageData.categoryItemWidth; // 全部分类的宽度
    });
  }
}

function organizeDataByColumns(data: any[]) {
  const numRows = 2;
  const organizedData = [];
  for (let i = 0; i < data.length; i += numRows) {
    const column = data.slice(i, i + numRows); // 提取当前列的数据
    organizedData.push(column); // 将当前列添加到结果数组中
  }
  return organizedData;
}

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}

const onLoginClick = async (position: any, type?: any) => {
  // 埋点
  if (position === "top" && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_register",
      "点击顶部导航注册，打开注册窗口"
    );
  } else if (position === "top" && type === 1) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_login",
      "点击顶部导航登录，打开登录窗口"
    );
  } else if (!position && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_home_body_register",
      "点击首页正文区注册"
    );
  }
  loginRegister?.openLogin("", type);
};

function onKeywordClick(event: any) {
  navigateToPage(
    `${goodsListAllPath}`,
    {
      keyword: pageData.keyword?.trim(),
    },
    true,
    event
  );
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

// 滚动步骤数据
const buySteps = [
  {
    image: selectionQuotation,
    title: "Selección & Cotización",
    desc: [
      "Explore millones de productos, agregue al carrito",
      "Envíe su solicitud (sin pago inmediato)",
    ],
  },
  {
    image: confirmationPayment,
    title: "Confirmación & Pago",
    desc: [
      "Nuestro equipo calcula costos finales (incluye envío e impuestos)",
      "Pago seguro tras confirmación",
    ],
  },
  {
    image: qualityControlShipping,
    title: "Control de Calidad & Envío",
    desc: [
      "Inspección manual + máquina en nuestro almacén",
      "Opciones de transporte aéreo/marítimo, seguimiento en tiempo real",
      "Entrega directa en su dirección",
    ],
  },
];

// 响应式数据
const currentStep = ref(0);
const progressLineHeight = ref(0);
const backgroundLineHeight = ref(0);
const stepRefs = ref<HTMLElement[]>([]);
const contentRefs = ref<HTMLElement[]>([]);
const stepAnimations = ref(new Array(buySteps.length).fill(false));
const stepGaps = ref<number[]>([]);

// 获取步骤圆圈的位置
const getStepPosition = (index: any) => {
  if (index === 0) return LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;

  let position = LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;
  for (let i = 0; i < index; i++) {
    position += stepGaps.value[i] || LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }
  return position;
};

// 配置常量
const LAYOUT_CONFIG = {
  STEP_CIRCLE_OFFSET: 9, // 步骤圆圈偏移
  MAIN_IMAGE_OFFSET: 24, // 主图偏移
  CIRCLE_RADIUS: 25, // 圆圈半径
  MIN_STEP_GAP: 150, // 最小步骤间距
  DEFAULT_STEP_GAP: 400, // 默认步骤间距
  SCROLL_OFFSET: 240, // 滚动偏移量
};

// 注释：移除了getMainImagePosition函数，因为现在显示所有步骤图片

// 计算步骤间距 - 基于右侧内容的实际位置动态计算
const calculateStepGaps = () => {
  nextTick(() => {
    if (contentRefs.value.length === 0) return;

    const gaps = [];
    for (let i = 0; i < buySteps.length - 1; i++) {
      const gap = calculateSingleStepGap(i);
      gaps.push(gap);
    }
    stepGaps.value = gaps;
  });
};

// 计算单个步骤间距
const calculateSingleStepGap = (index: number) => {
  const currentContent = contentRefs.value[index];
  const nextContent = contentRefs.value[index + 1];

  if (!currentContent || !nextContent) {
    return LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }

  const currentRect = currentContent.getBoundingClientRect();
  const nextRect = nextContent.getBoundingClientRect();

  // 计算实际距离并确保最小间距
  const actualDistance =
    nextRect.top - currentRect.top + LAYOUT_CONFIG.MAIN_IMAGE_OFFSET;
  return Math.max(actualDistance, LAYOUT_CONFIG.MIN_STEP_GAP);
};

// 计算当前激活的步骤
const calculateActiveStep = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;
  let activeStep = 0;

  for (let i = 0; i < contentRefs.value.length; i++) {
    const element = contentRefs.value[i];
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    const elementTop = rect.top + scrollTop;
    const elementMiddle =
      elementTop + rect.height / 2 - LAYOUT_CONFIG.SCROLL_OFFSET;

    if (scrollTop + windowHeight / 2 >= elementMiddle) {
      activeStep = i;
    }
  }
  return activeStep;
};

// 触发步骤动画
const triggerStepAnimation = (activeStep: number, previousStep: number) => {
  if (activeStep !== previousStep && activeStep < stepAnimations.value.length) {
    stepAnimations.value[activeStep] = false;
    nextTick(() => {
      stepAnimations.value[activeStep] = true;
    });
  }
};

// 更新进度线和背景线
const updateProgressLines = (activeStep: number) => {
  // 计算进度线高度
  if (activeStep === 0) {
    progressLineHeight.value = 0;
  } else {
    const targetStepPosition = getStepPosition(activeStep);
    const firstStepPosition = getStepPosition(0);
    progressLineHeight.value = Math.max(
      0,
      targetStepPosition - firstStepPosition
    );
  }

  // 计算背景线高度
  const lastStepPosition = getStepPosition(buySteps.length - 1);
  const firstStepPosition = getStepPosition(0);
  backgroundLineHeight.value = Math.max(
    0,
    lastStepPosition - firstStepPosition
  );
};

// 滚动监听逻辑
const handleScroll = () => {
  if (!contentRefs.value.length || !stepRefs.value.length) return;

  const activeStep = calculateActiveStep();
  const previousStep = currentStep.value;
  currentStep.value = activeStep;

  triggerStepAnimation(activeStep, previousStep);

  updateProgressLines(activeStep);
};

// 窗口大小变化处理函数
const handleResize = () => {
  calculateStepGaps();
};

// 初始化函数
const initializeLayout = () => {
  try {
    calculateStepGaps();
    handleScroll();
  } catch (error) {
    console.warn("Layout initialization failed:", error);
  }
};

// 生命周期钩子
onMounted(() => {
  initializeLayout();
  window.addEventListener("scroll", handleScroll, { passive: true });
  window.addEventListener("resize", handleResize, { passive: true });
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  window.removeEventListener("resize", handleResize);
});
</script>
<style lang="scss" scoped>
:deep(.n-scrollbar) {
  min-height: 380px !important;
}

:deep(.country-select) {
  .country-delivery {
    font-size: 16px;
    margin-right: 6px;
  }
  .country-code {
    font-size: 16px;
    line-height: 14px;
  }
}

.page-wrapper {
  width: 1280px;
  max-width: 1280px;
  margin: 0 auto;
}

.header-wrapper {
  width: 100%;
  height: 624px;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
}

// .benefit-breakdown {
//   background: linear-gradient(180deg, #ac2023 0%, #ac2023 100%);
// }

.header-and-searchbar {
  background-color: #fff;
  margin: 0px auto;
  position: relative;
  width: 100%;
  height: 624px;
  min-width: 1280px;
  .header-wrapper {
    width: 1280px;
    margin: 0px auto;
    position: relative;
    height: 120px;
  }
  .page-header {
    position: relative;
    width: 100%;
    font-size: 14px;
    line-height: 18px;
    z-index: 999;
    .sub-header {
      display: inline-flex;
      width: 1280px;
      padding: 0 40px;
    }
  }
  #page-header.page-fixed {
    position: fixed;
    top: 0;
    left: 0;
    border-bottom: 1px solid #ddd;
    background-color: #fff !important;
    .sub-header {
      display: none;
    }
  }
  #page-header.page-dark {
    background-color: transparent;
    color: #fff;
  }
  #page-header.page-white {
    border-bottom: 1px solid #ddd;
    color: #222;
    background-color: #fff !important;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90px;
    width: 1280px;
    margin: 0 auto;
    font-size: 14px;
    padding: 0 40px;
  }
  .content-container {
    display: flex;
    align-items: center;
    height: 470px;
    margin: 0 auto;
    position: relative;
    width: 1200px;
    .video-title {
      vertical-align: middle;
      color: rgb(255, 255, 255);
      font-size: 20px;
      font-weight: 400;
      line-height: 26px;
      text-align: center;
    }
    .content-title {
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      color: rgb(255, 255, 255);
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      line-height: 58px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 950px;
    }
    .content-desc {
      width: 740px;
      margin: 16px 0;
    }
    .image-search-inner {
      width: 530px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 12px;
      flex-shrink: 0;
      font-size: 18px;
      font-weight: 500;
      line-height: 18px;
      border-radius: 200px;
      background: #e50113;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
      cursor: pointer;
    }
    .home-search {
      height: 140px;
      margin-top: 32px;
      width: 530px;
      justify-content: flex-start;
      z-index: 5;
      .home-search-inner {
        position: relative;
        display: flex;
        width: 100%;
        border: 2px solid #e50113;
        border-radius: 100px;
        background-color: #fff;
        justify-content: center;
        align-items: center;
        border-radius: 30px;
        height: 50px;
        border-width: 0;
      }
      .search-bar-input-wrapper {
        position: relative;
        display: block;
        width: 100%;
        margin: 0 28px;
        box-sizing: border-box;
      }
      .search-bar-input {
        font-size: 16px;
        width: 100%;
        color: #222;
        background-color: #fff;
        line-height: 36px;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        background-image: none;
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
      }
      .search-bar-inner-button {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 125px;
        height: 36px;
        border-radius: 100px;
        font-size: 14px;
        line-height: 20px;
        background: #e50113;
        color: #fff;
        border: 0 solid transparent;
        cursor: pointer;
        border-radius: 26px;
        height: 42px;
        margin-right: 4px;
      }
      .bottom-recommend-wrap {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        box-sizing: border-box;
        padding: 0 22px;
        width: 100%;
        height: 26px;
        overflow: hidden;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        height: 36px;
        margin-top: 32px;
        padding: 0;
        .keyword {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid #fff;
          border-radius: 22px;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          font-size: 14px;
          height: 36px;
          line-height: 36px;
          margin: 0 10px;
          padding: 0 24px;
          text-decoration: none;
          &:hover {
            background-color: rgba(0, 0, 0, 0.5);
            text-decoration: none;
          }
        }
      }
    }
  }
}

.category-wrapper {
  min-width: 1280px;
  padding: 120px 0;
  min-height: 360px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 360px;
  .category-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
    margin: 0;
    width: 800px;
  }
  .category-number {
    width: 500px;
    display: grid;
    row-gap: 20px;
    column-gap: 20px;
    grid-template-columns: 1fr 1fr;
    height: fit-content;
    .number-item {
      padding-left: 16px;
      position: relative;
      &::before {
        background-color: #ddd;
        border-radius: 2px;
        content: "";
        display: inline-block;
        height: 86%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
      }
      span {
        color: #e50113;
        font-size: 36px;
        font-weight: 500;
        letter-spacing: -0.73px;
        line-height: 44px;
      }
    }
  }
  .category-list {
    min-width: 1200px;
    height: 320px;
    margin-top: 110px;
    overflow: hidden;
    position: relative;
    .slider_inner {
      display: flex;
      height: 100%;
      left: 0;
      padding-left: 0;
      padding-right: 0;
      position: absolute;
      top: 0;
      transition: transform 0.8s linear 0s;
      width: 100%;
      .category-row {
        height: 100%;
        width: 175px;
        a {
          color: inherit;
          text-decoration: inherit;
        }
        .category-item {
          align-items: center;
          border: 2px solid #eaeaea;
          border-radius: 70px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          height: 140px;
          justify-content: center;
          margin-bottom: 20px;
          margin-right: 35px;
          width: 140px;
          &:hover {
            border: 2px solid #e50113;
          }
          .img {
            display: initial;
            height: 38px;
            vertical-align: middle;
            width: 38px;
            padding: 5px;
            box-sizing: content-box;
          }
          .category-name {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              color: #222;
              display: -webkit-box;
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              overflow: hidden;
              padding: 0 4px;
              text-align: center;
              width: 100px;
            }
          }
        }
      }
    }
    .slider_arrow_wrapper {
      align-items: center;
      display: flex;
      height: 100%;
      position: absolute;
      top: 0;
      width: 80px;
      z-index: 9;
      .slider_arrow {
        background-color: #fff;
        border-radius: 48px;
        cursor: pointer;
        display: inline-block;
        font-size: 0;
        height: 48px;
        line-height: 48px;
        text-align: center;
        width: 48px;
        border: 1px solid #e5e5e5;
        -webkit-box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        img {
          display: inline;
          height: 28px;
          vertical-align: middle;
          width: 28px;
        }
      }
    }
    .slider_arrow_wrapper.prev {
      background-image: linear-gradient(270deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: start;
      left: 0;
      padding-left: 12px;
      img {
        transform: scaleX(-1);
      }
    }
    .slider_arrow_wrapper.next {
      background-image: linear-gradient(90deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: end;
      padding-right: 12px;
      right: 0;
    }
  }
}
.simplify-purchase {
  min-width: 1280px;
  padding: 80px 0;
  background-color: #f4f4f4;
  .purchase-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
    margin-bottom: 30px;
  }
  .purchase-desc {
    font-size: 20px;
  }
}
.security-floor {
  min-width: 1280px;
  position: relative;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  color: #000;
  margin: auto;
  overflow: hidden;
  width: 100%;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    z-index: 1;
  }
  .security-floor_wrapper {
    width: 1200px;
    margin: 80px auto;
    content-visibility: auto;
    contain-intrinsic-size: auto 1280px auto 772px;
    position: relative;
    z-index: 2;
    .security-floor_title {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      display: -webkit-box;
      font-size: 42px;
      font-weight: 500;
      line-height: 56px;
      margin-bottom: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 800px;
    }
    .security-floor_card {
      backdrop-filter: blur(50px);
      background: hsla(0, 0%, 100%, 0.11);
      border-radius: 20px;
      font-size: 16px;
      height: 260px; //todo340
      line-height: 22px;
      overflow: hidden;
      padding: 50px 46px;
      position: relative;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      width: 584px;
      .security-floor_icon {
        display: block;
        height: 50px;
        margin: 18px 6px 20px 0;
      }
      .security-floor_card_text_2 {
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        line-height: 22px;
      }
      .security-floor_watch {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #00000032;
        border: 1px solid #fff;
        border-radius: 999px;
        cursor: pointer;
        font-weight: 500;
        height: 50px;
        margin-inline-end: 20px;
        max-width: 320px;
        padding: 0 20px;
        &:hover {
          background-color: #00000080;
        }
      }
    }
  }
}
.full-link {
  min-width: 1280px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 720px;
  .full-link_box {
    color: #222;
    height: 720px;
    margin: auto;
    overflow: hidden;
    width: 1200px;
    display: flex;
    align-items: center;
    .full-link_title {
      -webkit-box-orient: vertical;
      color: #222;
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 56px;
      margin: 120px 0 0;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 860px;
    }
    .full-link_item_wrapper {
      -webkit-flex-direction: column;
      flex-direction: column;
      margin-bottom: 100px;
      margin-top: 100px;
      padding-inline-start: 40px;
      position: relative;
      width: 1200px;
      display: flex;
      margin-right: 40px;
      .full-title {
        font-weight: 500;
        color: #222;
        font-size: 42px;
        line-height: 52px;
        margin-bottom: 20px;
      }
      .full-link_item {
        cursor: pointer;
        width: 624px;
        z-index: 1;
        display: flex;
      }
      .full-link_item:not(:last-of-type) {
        margin-bottom: 20px;
      }
      .full-link_item:not(:first-child) {
        margin-top: 20px;
      }

      .full-link_icon {
        width: 30px;
        height: 30px;
        margin-inline-end: 16px;
        position: relative;
        flex-shrink: 0;
        background-color: #e50113;
        border-radius: 50%;
        margin-top: 30px;
      }
      .full-link_item_title {
        font-size: 28px;
        line-height: 34px;
        margin-top: 13px;
        max-width: 600px;
      }
      .full-link_item_desc {
        color: #222;
        display: -webkit-box;
        font-size: 16px;
        height: fit-content;
        line-height: 22px;
        margin-top: 12px;
        max-width: 521px;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .full-link_item_tail {
        background-color: #3a180b;
        height: 108%;
        left: 54px;
        position: absolute;
        width: 4px;
        border-radius: 2px;
        top: -10px;
      }
      .full-link_item_img {
        // height: 400px;
        max-width: 450px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 492px;
      }
    }
  }
}

.login-guide {
  min-width: 1280px;
  position: relative;
  align-items: center;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 382px;
  display: flex;
  flex-direction: column;
  font-size: 20px;
  height: 382px;
  overflow: hidden;
  text-align: center;
  width: 100%;
  color: #222;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    z-index: 1;
  }
  .login-guide-wrapper {
    margin: auto;
    width: 1000px;
    color: #000;
    position: relative;
    z-index: 2;

    .login-title {
      font-size: 44px;
      font-weight: 500;
      line-height: 56px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .login-desc {
      font-size: 20px;
      line-height: 26px;
      margin-top: 32px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .section_banner-button {
      font-size: 22px;
      line-height: 30px;
      padding: 30px 50px;
      border-radius: 30px;
      margin-top: 48px;
      box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
    }
  }
}
.user-video {
  min-width: 1280px;
  padding: 80px 120px;
  .user-video-wrapper {
    margin: auto;
    width: 1000px;
  }
  .video-title {
    font-size: 36px;
    font-weight: 500;
    line-height: 52px;
    text-align: center;
  }
  .video-wrapper {
    width: 220px;
    margin: 50px 0 30px 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 12px;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 36px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.custom-arrow-hot {
  .custom-arrow-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* 设置背景颜色为灰色 */
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

.n-card :deep(.n-card__content) {
  padding: 0.25rem 0.25rem;
}
:deep(.full-link_carousel.n-carousel .n-carousel__slides .n-carousel__slide) {
  text-align: center;
}
:deep(.login-button) {
  --n-border-hover: 1px solid #e50113 !important;
  --n-border-focus: 1px solid #e50113 !important;
  --n-text-color-hover: #e50113 !important;
  --n-text-color-focus: #e50113 !important;
}
:deep(.home-goods-image) {
  width: 181px !important;
  height: 181px !important;
}

// 过渡动画样式
.fade-slide-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-slide-leave-active {
  transition: none;
}

.fade-slide-enter-from {
  opacity: 0;
}

.fade-slide-leave-to {
  opacity: 0;
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
}

// 图片切换动画
.image-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.image-fade-leave-active {
  transition: none;
}

.image-fade-enter-from {
  opacity: 0;
}

.image-fade-leave-to {
  opacity: 0;
}

.image-fade-enter-to,
.image-fade-leave-from {
  opacity: 1;
}

// 步骤内容样式
.step-content {
  transition: opacity 0.3s ease-in-out;
  // opacity: 0.6;
}

.step-content.active {
  // opacity: 1;
}

// 步骤圆圈动画
.step-circle-animate {
  animation: circleProgress 0.6s ease-out;
}

@keyframes circleProgress {
  0% {
    stroke-dashoffset: 150.8;
  }
  100% {
    stroke-dashoffset: 0;
  }
}
</style>
