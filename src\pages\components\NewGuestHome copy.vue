<template>
  <div class="page-wrapper">
    <div class="header-and-searchbar text-white">
      <div class="header-bg">
        <div class="header-bg-wrapper">
          <n-image
            lazy
            preview-disabled
            :src="headerBg"
            style="height: 624px; width: 1920px"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
        </div>
      </div>
      <div class="header-wrapper text-white">
        <div
          id="page-header"
          class="page-header"
          :class="
            pageData.isHeaderFixed ? 'page-white page-fixed' : 'page-dark'
          "
        >
          <template v-if="!pageData.isHeaderFixed">
            <div class="header-content">
              <a class="w-[180px]" href="/" data-spm-box="navigation-logo-icon">
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  :src="pageTheme.homeLogo"
                  class="w-[180px]"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
              </a>

              <div class="flex">
                <div class="mr-4 w-[70px]">
                  <country-select
                    mode="popover"
                    @save="onSaveCountry"
                    spm="select_site_from_nav"
                  />
                </div>
                <n-button
                  @click="onLoginClick('top', 1)"
                  round
                  text-color="#fff"
                  class="relative mr-2 login-button"
                >
                  {{ authStore.i18n("cm_common_login") }}
                </n-button>
                <n-button
                  color="#e50113"
                  @click="onLoginClick('top', 0)"
                  round
                  class="relative"
                >
                  {{ authStore.i18n("cm_common_register") }}
                  <img
                    loading="lazy"
                    class="absolute z-10 w-[70px] right-[-20px] top-[-40px]"
                    src="@/assets/icons/gift.webp"
                    referrerpolicy="no-referrer"
                  />
                </n-button>
              </div>
            </div>
            <div class="sub-header inline-flex justify-between items-center">
              <search-tabs
                :noCarouselCate="true"
                :categories="props.categories"
              >
                <template #default="{ onMouseEnter, onMouseLeave }">
                  <div
                    class="inline-flex items-center cursor-pointer ml-[-24px]"
                    @mouseenter="onMouseEnter"
                    @mouseleave="onMouseLeave"
                  >
                    <icon-card
                      name="system-uicons:list"
                      size="26"
                      class="mr-0.5"
                    ></icon-card>
                    <span class="text-base">{{
                      authStore.i18n("cm_guestHome.allCategory")
                    }}</span>
                  </div>
                </template>
              </search-tabs>

              <div
                class="absolute right-[40px] flex items-center"
                data-spm-box="navigation-top-article"
              >
                <n-space :style="{ gap: '0 28px' }">
                  <a
                    class="hover:underline cursor-pointer"
                    v-for="(news, index) in newsData"
                    :key="index"
                    :href="news.path"
                    target="_blank"
                  >
                    {{ news.title }}
                  </a>
                </n-space>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="header-content">
              <a class="w-[180px]" href="/" data-spm-box="navigation-logo-icon">
                <n-image
                  lazy
                  object-fit="fill"
                  preview-disabled
                  :src="pageTheme.logo"
                  class="w-[180px]"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
              </a>
              <text-image-search v-if="pageData.isHeaderButtonFixed">
              </text-image-search>
              <div class="flex">
                <div class="mr-4 w-[70px]">
                  <country-select
                    mode="popover"
                    @save="onSaveCountry"
                    spm="select_site_from_nav"
                  />
                </div>
                
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="content-container !h-[460px]">
        <div>
          <image-search-multi placement="bottom-start">
            <div class="image-search-inner">
              <img
                loading="lazy"
                src="@/assets/icons/imageSearch.svg"
                alt="search"
                class="w-[32px] mr-[8px]"
                referrerpolicy="no-referrer"
              />
              <span>{{ authStore.i18n("cm_home.imageSearch") }}</span>
            </div>
          </image-search-multi>

          <div
            class="home-search !h-[80px] !mt-0"
            data-spm-box="navigation-keyword-search"
          >
            <div class="home-search-inner" id="page-header-button">
              <div class="search-bar-input-wrapper">
                <input
                  class="search-bar-input"
                  type="text"
                  maxlength="50"
                  v-model.trim="pageData.keyword"
                  @keyup.enter="onKeywordClick($event)"
                  :placeholder="authStore.i18n('cm_home.searchPlaceholder')"
                />
              </div>
              <div class="flex items-center">
                <button
                  class="search-bar-inner-button ml-2"
                  @click="onKeywordClick($event)"
                >
                  <img
                    loading="lazy"
                    src="@/assets/icons/search.svg"
                    alt="search"
                    class="w-[24px] mr-[4px]"
                    referrerpolicy="no-referrer"
                  />
                  <span class="text-[18px] leading-[18px] font-medium">{{
                    authStore.i18n("cm_home.search")
                  }}</span>
                </button>
              </div>

              <div class="popup common-panel association-popup"></div>
            </div>
          </div>
          <div class="content-title">
            {{ authStore.i18n("cm_guestHome.headerTitle") }}
          </div>
          <div class="content-desc">
            {{ authStore.i18n("cm_guestHome.headerDesc") }}
          </div>
        </div>
      </div>
    </div>

    <div
      class="w-[1200px] mx-auto flex justify-center items-center text-center my-3 text-[#e50113] text-base"
    >
      <img
        loading="lazy"
        :src="noticeLogo"
        class="mr-1 w-[36px]"
        referrerpolicy="no-referrer"
      />
      {{ authStore.i18n("cm_guestHome.noticeTitle") }}
    </div>

    <!-- 一站式购物服务-->
    <div class="service-wrapper">
      <div class="w-[1200px] mx-auto">
        <div class="flow_wrapper">
          <div v-for="(step, index) in serviceData" :key="index" class="flex">
            <div class="flow-item-wrapper">
              <n-image
                lazy
                preview-disabled
                class="img_box"
                :src="step.img"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              ></n-image>
              <div class="item-title">
                {{ step.title }}
              </div>
              <div class="item-content">
                <n-space vertical :style="{ gap: '12px 0' }">
                  <div
                    v-for="(content, contentIndex) in step.content"
                    :key="contentIndex"
                  >
                    {{ content }}
                  </div>
                </n-space>
              </div>
            </div>
            <img
              loading="lazy"
              :src="stepIconImg"
              class="img_icon"
              v-if="index < serviceData.length - 1"
              referrerpolicy="no-referrer"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="min-w-[1280px] pt-[80px]">
      <!-- 热销货盘 -->
      <hot-pallets
        :selectorList="props.selectorList"
        :recommendSupplierGoods="props.recommendSupplierGoods"
        :habitableCapsuleGoods="props.habitableCapsuleGoods"
        class="w-[1200px] mx-auto"
      ></hot-pallets>
    </div>

    <!-- 降低成本，增加效益 -->
    <div class="simplify-purchase">
      <div class="w-[1200px] mx-auto flex items-center">
        <div class="basis-2/3 mr-10">
          <div class="purchase-title">
            {{ authStore.i18n("cm_guestHome.purchaseTitle") }}
          </div>
          <div class="purchase-desc">
            {{ authStore.i18n("cm_guestHome.purchaseDesc") }}
          </div>
        </div>
        <img
          loading="lazy"
          :src="purchaseImg"
          class="w-[360px]"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>

    <!-- 商品分类 -->
    <div class="category-wrapper" data-spm-box="homepage-top-categories">
      <div class="w-[1200px] mx-auto">
        <div class="h-52 flex justify-between">
          <div class="category-title">
            {{ authStore.i18n("cm_guestHome.categoryTitle") }}
          </div>
          <div class="category-number">
            <div
              class="number-item"
              v-for="(item, index) in cateIntroData"
              :key="index"
            >
              <span>{{ item.number }}</span>
              <p>
                {{ item.title }}
              </p>
            </div>
          </div>
        </div>
        <!-- <div class="category-list">
          <div
            class="slider_arrow_wrapper prev"
            @click="prevSlide"
            v-show="pageData.showPrevArrow"
          >
            <div class="slider_arrow prev">
              <img loading="lazy" :src="arrowIcon" referrerpolicy="no-referrer" />
              />
            </div>
          </div>
          <div
            class="slider_inner"
            :style="{ transform: `translateX(${pageData.slidDis}px)` }"
          >
            <div
              class="category-row"
              v-for="(cate, index) in pageData.categoryData"
              :key="index"
            >
              <a
                v-for="cateItem in cate"
                :key="cateItem.id"
                v-bind:data-spm-index="index + 1"
                :href="`/goods/list/${cateItem.id}?cateName=${cateItem.name}`"
                target="_blank"
                ><div class="category-item">
                  <n-image lazy preview-disabled :src="cateItem.cateLogo" class="img" :img-props="{ referrerpolicy: 'no-referrer' }" />
                  <div class="category-name">
                    <span>{{ cateItem.name }}</span>
                  </div>
                </div></a
              >
            </div>
          </div>
          <div
            class="slider_arrow_wrapper next"
            @click="nextSlide"
            v-show="pageData.showNextArrow"
          >
            <div class="slider_arrow next">
              <img loading="lazy" :src="arrowIcon" referrerpolicy="no-referrer" />
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- 安全 -->
    <div class="security-floor" :style="`background-image: url(${securityBg})`">
      <div class="security-floor_wrapper">
        <div class="security-floor_title">
          {{ authStore.i18n("cm_guestHome.securityTitle") }}
        </div>
        <div class="flex justify-between">
          <div
            class="security-floor_card"
            v-for="(item, index) in securityData"
            :key="index"
          >
            <div class="flex items-center">
              <n-image
                lazy
                preview-disabled
                class="security-floor_icon"
                :src="item.icon"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              />
              <div class="font-medium text-2xl">
                {{ item.title }}
              </div>
            </div>
            <div class="security-floor_card_text_2">
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 为什么选择我们？ -->
    <div class="full-link">
      <div class="full-link_box">
        <ul class="full-link_item_wrapper">
          <div class="full-title">
            {{ authStore.i18n("cm_guestHome.fullTitle") }}
          </div>
          <li
            class="full-link_item"
            v-for="(link, index) in chooseData"
            :key="index"
          >
            <!-- <div class="full-link_icon"></div> -->
            <div class="full-link_content">
              <div class="full-link_item_title">
                <span class="text-[#e50113] text-[32px]">0{{ index + 1 }}</span>
                {{ link.title }}
              </div>
              <div class="full-link_item_desc">
                {{ link.desc }}
              </div>
            </div>
          </li>

          <!-- <div class="full-link_item_tail"></div> -->
        </ul>
        <div class="h-[300px]">
          <n-carousel
            autoplay
            show-dots
            style="height: 300px"
            :transition-style="{ transitionDuration: '500ms' }"
            :interval="3000"
            class="full-link_carousel"
          >
            <n-image
              lazy
              preview-disabled
              :src="carousel"
              v-for="(carousel, index) in carouselData"
              :key="index"
              class="h-[300px]"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
          </n-carousel>
        </div>
      </div>
    </div>

    <!-- 注册登录 -->
    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">
          {{ authStore.i18n("cm_guestHome.loginTitle") }}
        </div>
        <div class="login-desc">
          {{ authStore.i18n("cm_guestHome.loginDesc") }}
        </div>
        <n-button
          color="#e50113"
          class="section_banner-button"
          @click="onLoginClick('', 0)"
        >
          {{ authStore.i18n("cm_common_registerNow") }}
        </n-button>
      </div>
    </div>

    <!-- 客户评价 -->
    <div class="user-video">
      <div class="user-video-wrapper">
        <div class="video-title">
          {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
        </div>
        <div class="flex justify-between">
          <div
            class="video-wrapper"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video, index)"
          >
            <n-image
              lazy
              preview-disabled
              :src="video.videoBg"
              class="img"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
            <div class="video-icon">
              <icon-card
                name="mingcute:play-fill"
                size="20"
                color="#322623"
              ></icon-card>
            </div>
          </div>
        </div>
      </div>
    </div>
    <page-footer></page-footer>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
  <login-register-modal ref="loginRegisterModalRef"></login-register-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import LoginRegisterModal from "./LoginRegisterModal.vue";
import HotPallets from "./HotSelectors.vue";

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");

interface selectorItem {
  spmCode: string;
  name: string;
  tagId: string;
  goodsList: any[];
}

const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
  hotKeywords: {
    type: Array,
    default: () => [],
  },
  selectorList: {
    type: Array as () => selectorItem[],
    default: () => [],
  },
  recommendSupplierGoods: {
    type: Object,
    default: () => <any>{},
  },
  habitableCapsuleGoods: {
    type: Object,
    default: () => <any>{},
  },
});

const pageData = reactive(<any>{
  slidDis: 0,
  showNextArrow: true,
  showPrevArrow: false,
  categoryData: <any>[],
  categoryInRow: 7,
  categoryAllWidth: 0, //所有分类的宽度
  categoryItemWidth: 175, // 分类子项目的宽度
  categoryContainerWidth: 0, //分类容器的宽度
  activatedLinkIndex: 0,
  isHeaderFixed: false,
  isHeaderButtonFixed: false,
  showKwCarousel: true, //是否展示轮播关键词
  mercadoArrow: false,
  keyword: "",
});

const videoModalRef = ref<any>(null);
const pageTheme = computed(() => useConfigStore().getPageTheme);

import headerBg from "@/assets/icons/guestHome.jpg";
const noticeLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/268c3fd6-85ef-4db5-8640-549d908d570b.png";
const arrowIcon =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/24/47431a43-32e6-4442-8da5-4ef1609e966e.png";
const purchaseImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/e7a794cc-524c-485c-bc14-50932983530c.png";
const securityBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/158870e0-a43f-40e7-a53a-2cf4fad3c3b7.jpg";
const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/6d5472a2-a993-481f-ad19-9ab151d8e7ae.jpg";
const stepIconImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/30/6ca30da6-9b4b-4f89-85ba-89c6efc8e261.png";
const step1Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/c25eb123-2827-4975-b1bc-de430fd6048f.png";
const step2Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f7ac575b-89ac-49e9-be5c-afc4b8e88700.png";
const step3Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/21d38827-c33d-4772-9ea1-7a109dc175ec.png";
const step4Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/9b3fe249-e8d8-46a5-8137-c71bc9d524fa.png";

const newsData = [
  {
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    title: authStore.i18n("cm_news.helpCenter"),
    path: "/article/help-center",
  },
];

const serviceData = [
  {
    img: step1Img,
    title: authStore.i18n("cm_guestHome.chooseGoods"),
    content: [
      authStore.i18n("cm_guestHome.addGoods"),
      authStore.i18n("cm_guestHome.orderGoods"),
    ],
  },
  {
    img: step2Img,
    title: authStore.i18n("cm_guestHome.confirmPrice"),
    content: [
      authStore.i18n("cm_guestHome.countPrice"),
      authStore.i18n("cm_guestHome.predictPrice"),
      authStore.i18n("cm_guestHome.payPrice"),
    ],
  },
  {
    img: step3Img,
    title: authStore.i18n("cm_guestHome.payProduct"),
    content: [
      authStore.i18n("cm_guestHome.transProduct"),
      authStore.i18n("cm_guestHome.checkProduct"),
      authStore.i18n("cm_guestHome.storageProduct"),
    ],
  },
  {
    img: step4Img,
    title: authStore.i18n("cm_guestHome.interLogistics"),
    content: [
      authStore.i18n("cm_guestHome.chooseLogistics"),
      authStore.i18n("cm_guestHome.trackLogistics"),
      authStore.i18n("cm_guestHome.confirmLogistics"),
    ],
  },
];

const cateIntroData = [
  {
    number: authStore.i18n("cm_guestHome.number1"),
    title: authStore.i18n("cm_guestHome.numberDesc1"),
  },
  {
    number: authStore.i18n("cm_guestHome.number2"),
    title: authStore.i18n("cm_guestHome.numberDesc2"),
  },
];

const securityData = [
  {
    title: authStore.i18n("cm_guestHome.securityQual"),
    desc: authStore.i18n("cm_guestHome.securityQualDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3447a23e-9bb7-4824-a978-97ee506721b8.png",
  },
  {
    title: authStore.i18n("cm_guestHome.securityPay"),
    desc: authStore.i18n("cm_guestHome.securityPayDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/001a3c9b-c5b1-42be-8eb9-ca6eb718d918.png",
  },
];

const chooseData = [
  {
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const carouselData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

async function onScroll(e: any) {
  // y轴滚动
  if (e.deltaY !== 0) {
    let scrollTop =
      window.pageYOffset ||
      document.documentElement.scrollTop ||
      document.body.scrollTop;

    const pageHeader = document.querySelector("#page-header") as HTMLElement;
    const headerHeight = pageHeader.offsetHeight;
    const pageSearchButton =
      document.querySelector("#page-header-button") || <any>{};
    // 判断是否显示固定搜索
    if (scrollTop > 0) {
      pageData.isHeaderFixed = true;
    } else {
      pageData.isHeaderFixed = false;
    }

    if (
      scrollTop >
      pageSearchButton.offsetTop + pageSearchButton.offsetHeight / 2
    ) {
      pageData.isHeaderButtonFixed = true;
    } else {
      pageData.isHeaderButtonFixed = false;
    }
  }
}

function nextSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  ); // 向上取整
  const restItems = pageData.categoryData.length - slidItems;
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, restItems); // 最多滑动5个项目或少于5个

  if (restItems > 0) {
    pageData.slidDis -= pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动后已经到达或超过了总宽度，则隐藏“下一箭头”
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.slidDis = -(
        pageData.categoryAllWidth - pageData.categoryContainerWidth
      ); // 保证不会超出边界
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showNextArrow = false; // 没有剩余项目，隐藏下一箭头
  }

  // 始终显示上一箭头
  pageData.showPrevArrow = true;
}

function prevSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  );
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, slidItems);
  if (slidItems > 0) {
    pageData.slidDis += pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动回到第一屏，隐藏“上一箭头”
    if (pageData.slidDis >= 0) {
      pageData.slidDis = 0; // 确保不会超出边界
      pageData.showPrevArrow = false;
    } else {
      pageData.showPrevArrow = true;
    }

    // 始终显示“下一箭头”，除非已经滑到最末尾
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showPrevArrow = false;
  }
}

onPageCategoryData();
async function onPageCategoryData() {
  const res: any = await useHomePageCategory({});
  if (res?.result?.code === 200) {
    pageData.categoryData = organizeDataByColumns(res?.data);
    pageData.showNextArrow =
      pageData.categoryData.length > pageData.categoryInRow ? true : false;
    nextTick(() => {
      // 分类子项目的宽度
      const element = document?.querySelector(".category-row a");
      if (!element) return;
      pageData.categoryItemWidth = element.getBoundingClientRect().width || 175;
      pageData.categoryContainerWidth =
        document?.querySelector(".slider_inner")?.offsetWidth; // 分类容器的宽度
      pageData.categoryAllWidth =
        pageData.categoryData.length * pageData.categoryItemWidth; // 全部分类的宽度
    });
  }
}

function organizeDataByColumns(data: any[]) {
  const numRows = 2;
  const organizedData = [];
  for (let i = 0; i < data.length; i += numRows) {
    const column = data.slice(i, i + numRows); // 提取当前列的数据
    organizedData.push(column); // 将当前列添加到结果数组中
  }
  return organizedData;
}

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}

const onLoginClick = async (position: any, type?: any) => {
  // 埋点
  if (position === "top" && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_register",
      "点击顶部导航注册，打开注册窗口"
    );
  } else if (position === "top" && type === 1) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_login",
      "点击顶部导航登录，打开登录窗口"
    );
  } else if (!position && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_home_body_register",
      "点击首页正文区注册"
    );
  }
  loginRegister?.openLogin("", type);
};

function onKeywordClick(event: any) {
  navigateToPage(
    `${goodsListAllPath}`,
    {
      keyword: pageData.keyword?.trim(),
    },
    true,
    event
  );
}

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};
</script>
<style lang="scss" scoped>
:deep(.n-scrollbar) {
  min-height: 380px !important;
}
.page-wrapper {
  max-width: 1920px;
  margin: 0 auto;
}
.header-and-searchbar {
  background-color: #fff;
  margin: 0px auto;
  position: relative;
  width: 100%;
  height: 624px;
  min-width: 1280px;
  .header-wrapper {
    width: 1280px;
    margin: 0px auto;
    position: relative;
    height: 120px;
  }
  .page-header {
    position: relative;
    width: 100%;
    font-size: 14px;
    line-height: 18px;
    z-index: 999;
    .sub-header {
      display: inline-flex;
      width: 1280px;
      padding: 0 40px;
    }
  }
  #page-header.page-fixed {
    position: fixed;
    top: 0;
    left: 0;
    border-bottom: 1px solid #ddd;
    background-color: #fff !important;
    .sub-header {
      display: none;
    }
  }
  #page-header.page-dark {
    background-color: transparent;
    color: #fff;
  }
  #page-header.page-white {
    border-bottom: 1px solid #ddd;
    color: #222;
    background-color: #fff !important;
  }

  .header-bg {
    width: 100%;
    height: 624px;
    overflow: hidden;
    position: absolute;

    .header-bg-wrapper {
      height: 624px;
      left: 50%;
      position: absolute;
      top: 0px;
      transform: translateX(-50%);
      width: 1920px;
    }
    :deep(.n-image img) {
      width: 100%;
      height: 100%;
    }
  }
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90px;
    width: 1280px;
    margin: 0 auto;
    font-size: 14px;
    padding: 0 40px;
  }
  .content-container {
    display: flex;
    align-items: center;
    height: 470px;
    margin: 0 auto;
    position: relative;
    width: 1200px;
    .video-title {
      vertical-align: middle;
      color: rgb(255, 255, 255);
      font-size: 20px;
      font-weight: 400;
      line-height: 26px;
      text-align: center;
    }
    .content-title {
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      color: rgb(255, 255, 255);
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      line-height: 58px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 950px;
    }
    .content-desc {
      width: 740px;
      margin: 16px 0;
    }
    .image-search-inner {
      width: 530px;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 12px;
      flex-shrink: 0;
      font-size: 18px;
      font-weight: 500;
      line-height: 18px;
      border-radius: 200px;
      background: #e50113;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
      cursor: pointer;
    }
    .home-search {
      height: 140px;
      margin-top: 32px;
      width: 530px;
      justify-content: flex-start;
      z-index: 5;
      .home-search-inner {
        position: relative;
        display: flex;
        width: 100%;
        border: 2px solid #e50113;
        border-radius: 100px;
        background-color: #fff;
        justify-content: center;
        align-items: center;
        border-radius: 30px;
        height: 50px;
        border-width: 0;
      }
      .search-bar-input-wrapper {
        position: relative;
        display: block;
        width: 100%;
        margin: 0 28px;
        box-sizing: border-box;
      }
      .search-bar-input {
        font-size: 16px;
        width: 100%;
        color: #222;
        background-color: #fff;
        line-height: 36px;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        background-image: none;
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
      }
      .search-bar-inner-button {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 125px;
        height: 36px;
        border-radius: 100px;
        font-size: 14px;
        line-height: 20px;
        background: #e50113;
        color: #fff;
        border: 0 solid transparent;
        cursor: pointer;
        border-radius: 26px;
        height: 42px;
        margin-right: 4px;
      }
      .bottom-recommend-wrap {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        box-sizing: border-box;
        padding: 0 22px;
        width: 100%;
        height: 26px;
        overflow: hidden;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        height: 36px;
        margin-top: 32px;
        padding: 0;
        .keyword {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid #fff;
          border-radius: 22px;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          font-size: 14px;
          height: 36px;
          line-height: 36px;
          margin: 0 10px;
          padding: 0 24px;
          text-decoration: none;
          &:hover {
            background-color: rgba(0, 0, 0, 0.5);
            text-decoration: none;
          }
        }
      }
    }
  }
}
.service-wrapper {
  min-width: 1280px;
  padding: 56px 0;
  background-color: #f4f4f4;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 830px;
  .service-title {
    font-weight: 500;
    font-size: 42px;
    line-height: 52px;
    text-align: center;
    color: #222;
    margin-bottom: 40px;
  }
  .flow_wrapper {
    display: flex;
    justify-content: space-evenly;
    .flow-item-wrapper {
      width: 280px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .item-title {
        width: 250px;
        font-size: 20px;
        font-weight: 500;
        margin: 30px 0 20px 0;
        height: 60px;
      }
      .item-content {
        width: 250px;
        font-size: 15px;
      }
    }
    .img_box {
      width: 100px;
      height: 100px;
    }
    .img_icon {
      width: 30px;
      height: 30px;
      margin-top: 50px;
    }
  }
}
.category-wrapper {
  min-width: 1280px;
  padding: 120px 0;
  min-height: 360px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 360px;
  .category-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
    margin: 0;
    width: 800px;
  }
  .category-number {
    width: 500px;
    display: grid;
    row-gap: 20px;
    column-gap: 20px;
    grid-template-columns: 1fr 1fr;
    height: fit-content;
    .number-item {
      padding-left: 16px;
      position: relative;
      &::before {
        background-color: #ddd;
        border-radius: 2px;
        content: "";
        display: inline-block;
        height: 86%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
      }
      span {
        color: #e50113;
        font-size: 36px;
        font-weight: 500;
        letter-spacing: -0.73px;
        line-height: 44px;
      }
    }
  }
  .category-list {
    min-width: 1200px;
    height: 320px;
    margin-top: 110px;
    overflow: hidden;
    position: relative;
    .slider_inner {
      display: flex;
      height: 100%;
      left: 0;
      padding-left: 0;
      padding-right: 0;
      position: absolute;
      top: 0;
      transition: transform 0.8s linear 0s;
      width: 100%;
      .category-row {
        height: 100%;
        width: 175px;
        a {
          color: inherit;
          text-decoration: inherit;
        }
        .category-item {
          align-items: center;
          border: 2px solid #eaeaea;
          border-radius: 70px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          height: 140px;
          justify-content: center;
          margin-bottom: 20px;
          margin-right: 35px;
          width: 140px;
          &:hover {
            border: 2px solid #e50113;
          }
          .img {
            display: initial;
            height: 38px;
            vertical-align: middle;
            width: 38px;
            padding: 5px;
            box-sizing: content-box;
          }
          .category-name {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              color: #222;
              display: -webkit-box;
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              overflow: hidden;
              padding: 0 4px;
              text-align: center;
              width: 100px;
            }
          }
        }
      }
    }
    .slider_arrow_wrapper {
      align-items: center;
      display: flex;
      height: 100%;
      position: absolute;
      top: 0;
      width: 80px;
      z-index: 9;
      .slider_arrow {
        background-color: #fff;
        border-radius: 48px;
        cursor: pointer;
        display: inline-block;
        font-size: 0;
        height: 48px;
        line-height: 48px;
        text-align: center;
        width: 48px;
        border: 1px solid #e5e5e5;
        -webkit-box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        img {
          display: inline;
          height: 28px;
          vertical-align: middle;
          width: 28px;
        }
      }
    }
    .slider_arrow_wrapper.prev {
      background-image: linear-gradient(270deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: start;
      left: 0;
      padding-left: 12px;
      img {
        transform: scaleX(-1);
      }
    }
    .slider_arrow_wrapper.next {
      background-image: linear-gradient(90deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: end;
      padding-right: 12px;
      right: 0;
    }
  }
}
.simplify-purchase {
  min-width: 1280px;
  padding: 80px 0;
  background-color: #f4f4f4;
  .purchase-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
    margin-bottom: 30px;
  }
  .purchase-desc {
    font-size: 20px;
  }
}
.security-floor {
  min-width: 1280px;
  position: relative;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  color: #000;
  margin: auto;
  overflow: hidden;
  width: 100%;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    z-index: 1;
  }
  .security-floor_wrapper {
    width: 1200px;
    margin: 80px auto;
    content-visibility: auto;
    contain-intrinsic-size: auto 1280px auto 772px;
    position: relative;
    z-index: 2;
    .security-floor_title {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      display: -webkit-box;
      font-size: 42px;
      font-weight: 500;
      line-height: 56px;
      margin-bottom: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 800px;
    }
    .security-floor_card {
      backdrop-filter: blur(50px);
      background: hsla(0, 0%, 100%, 0.11);
      border-radius: 20px;
      font-size: 16px;
      height: 260px; //todo340
      line-height: 22px;
      overflow: hidden;
      padding: 50px 46px;
      position: relative;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      width: 584px;
      .security-floor_icon {
        display: block;
        height: 50px;
        margin: 18px 6px 20px 0;
      }
      .security-floor_card_text_2 {
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        line-height: 22px;
      }
      .security-floor_watch {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #00000032;
        border: 1px solid #fff;
        border-radius: 999px;
        cursor: pointer;
        font-weight: 500;
        height: 50px;
        margin-inline-end: 20px;
        max-width: 320px;
        padding: 0 20px;
        &:hover {
          background-color: #00000080;
        }
      }
    }
  }
}
.full-link {
  min-width: 1280px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 720px;
  .full-link_box {
    color: #222;
    height: 720px;
    margin: auto;
    overflow: hidden;
    width: 1200px;
    display: flex;
    align-items: center;
    .full-link_title {
      -webkit-box-orient: vertical;
      color: #222;
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 56px;
      margin: 120px 0 0;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 860px;
    }
    .full-link_item_wrapper {
      -webkit-flex-direction: column;
      flex-direction: column;
      margin-bottom: 100px;
      margin-top: 100px;
      padding-inline-start: 40px;
      position: relative;
      width: 1200px;
      display: flex;
      margin-right: 40px;
      .full-title {
        font-weight: 500;
        color: #222;
        font-size: 42px;
        line-height: 52px;
        margin-bottom: 20px;
      }
      .full-link_item {
        cursor: pointer;
        width: 624px;
        z-index: 1;
        display: flex;
      }
      .full-link_item:not(:last-of-type) {
        margin-bottom: 20px;
      }
      .full-link_item:not(:first-child) {
        margin-top: 20px;
      }

      .full-link_icon {
        width: 30px;
        height: 30px;
        margin-inline-end: 16px;
        position: relative;
        flex-shrink: 0;
        background-color: #e50113;
        border-radius: 50%;
        margin-top: 30px;
      }
      .full-link_item_title {
        font-size: 28px;
        line-height: 34px;
        margin-top: 13px;
        max-width: 600px;
      }
      .full-link_item_desc {
        color: #222;
        display: -webkit-box;
        font-size: 16px;
        height: fit-content;
        line-height: 22px;
        margin-top: 12px;
        max-width: 521px;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .full-link_item_tail {
        background-color: #3a180b;
        height: 108%;
        left: 54px;
        position: absolute;
        width: 4px;
        border-radius: 2px;
        top: -10px;
      }
      .full-link_item_img {
        // height: 400px;
        max-width: 450px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 492px;
      }
    }
  }
}

.login-guide {
  min-width: 1280px;
  position: relative;
  align-items: center;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 382px;
  display: flex;
  flex-direction: column;
  font-size: 20px;
  height: 382px;
  overflow: hidden;
  text-align: center;
  width: 100%;
  color: #222;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    z-index: 1;
  }
  .login-guide-wrapper {
    margin: auto;
    width: 1000px;
    color: #000;
    position: relative;
    z-index: 2;

    .login-title {
      font-size: 44px;
      font-weight: 500;
      line-height: 56px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .login-desc {
      font-size: 20px;
      line-height: 26px;
      margin-top: 32px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .section_banner-button {
      font-size: 22px;
      line-height: 30px;
      padding: 30px 50px;
      border-radius: 30px;
      margin-top: 48px;
      box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
    }
  }
}
.user-video {
  min-width: 1280px;
  padding: 80px 120px;
  .user-video-wrapper {
    margin: auto;
    width: 1000px;
  }
  .video-title {
    font-size: 36px;
    font-weight: 500;
    line-height: 52px;
    text-align: center;
  }
  .video-wrapper {
    width: 220px;
    margin: 50px 0 30px 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 12px;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 36px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.custom-arrow-hot {
  .custom-arrow-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* 设置背景颜色为灰色 */
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

.n-card :deep(.n-card__content) {
  padding: 0.25rem 0.25rem;
}
:deep(.full-link_carousel.n-carousel .n-carousel__slides .n-carousel__slide) {
  text-align: center;
}
:deep(.login-button) {
  --n-border-hover: 1px solid #e50113 !important;
  --n-border-focus: 1px solid #e50113 !important;
  --n-text-color-hover: #e50113 !important;
  --n-text-color-focus: #e50113 !important;
}
:deep(.home-goods-image) {
  width: 181px !important;
  height: 181px !important;
}
</style>
