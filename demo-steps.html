<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买步骤演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .step-image-overlay {
            transition: opacity 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">购买步骤样式演示</h1>
        
        <!-- 步骤标题 -->
        <div class="text-center mb-12">
            <div class="relative w-20 h-20 flex items-center justify-center mx-auto mb-4">
                <div class="w-20 h-20 rounded-full border-2 border-red-500 flex items-center justify-center">
                    <span class="text-4xl text-red-500 font-bold">3</span>
                </div>
            </div>
            <h2 class="text-2xl font-medium mb-2">3 pasos para comprar, simple y eficiente</h2>
            <div class="w-12 h-1 bg-red-500 mx-auto"></div>
        </div>

        <div class="flex gap-16">
            <!-- 左侧图片区域 -->
            <div class="w-96 flex flex-col space-y-16">
                <div class="relative w-full h-48 step-image" data-step="0">
                    <img src="https://via.placeholder.com/400x200/4F46E5/FFFFFF?text=Step+1+Image" 
                         alt="步骤 1" 
                         class="w-full h-48 object-cover rounded-lg">
                    <!-- 蒙层效果 -->
                    <div class="step-image-overlay absolute inset-0 bg-white bg-opacity-60 rounded-lg"></div>
                </div>
                
                <div class="relative w-full h-48 step-image" data-step="1">
                    <img src="https://via.placeholder.com/400x200/10B981/FFFFFF?text=Step+2+Image" 
                         alt="步骤 2" 
                         class="w-full h-48 object-cover rounded-lg">
                    <!-- 蒙层效果 -->
                    <div class="step-image-overlay absolute inset-0 bg-white bg-opacity-60 rounded-lg hidden"></div>
                </div>
                
                <div class="relative w-full h-48 step-image" data-step="2">
                    <img src="https://via.placeholder.com/400x200/F59E0B/FFFFFF?text=Step+3+Image" 
                         alt="步骤 3" 
                         class="w-full h-48 object-cover rounded-lg">
                    <!-- 蒙层效果 -->
                    <div class="step-image-overlay absolute inset-0 bg-white bg-opacity-60 rounded-lg"></div>
                </div>
            </div>

            <!-- 中间步骤线区域 -->
            <div class="w-12 relative flex flex-col items-center min-h-full">
                <!-- 背景线 -->
                <div class="absolute left-1/2 transform -translate-x-1/2 w-px bg-gray-200 h-full top-6"></div>
                <!-- 进度线 -->
                <div class="absolute left-1/2 transform -translate-x-1/2 w-px bg-red-500 transition-all duration-500" 
                     style="top: 25px; height: 200px;"></div>

                <!-- 步骤点 -->
                <div class="step-circle relative w-12 h-12 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center mb-40" data-step="0">
                    <span class="text-lg font-medium text-gray-500">1</span>
                </div>
                
                <div class="step-circle relative w-12 h-12 bg-white rounded-full border-2 border-red-500 flex items-center justify-center mb-40" data-step="1">
                    <span class="text-lg font-medium text-red-500">2</span>
                </div>
                
                <div class="step-circle relative w-12 h-12 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center" data-step="2">
                    <span class="text-lg font-medium text-gray-500">3</span>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="flex-1 flex flex-col space-y-16">
                <div class="step-content" data-step="0">
                    <h3 class="text-xl font-medium py-6 border-b border-gray-200 mb-8">Selección & Cotización</h3>
                    <div class="space-y-6">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Explore millones de productos, agregue al carrito</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Envíe su solicitud (sin pago inmediato)</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-content" data-step="1">
                    <h3 class="text-xl font-medium py-6 border-b border-gray-200 mb-8">Confirmación & Pago</h3>
                    <div class="space-y-6">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Nuestro equipo calcula costos finales (incluye envío e impuestos)</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Pago seguro tras confirmación</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-content" data-step="2">
                    <h3 class="text-xl font-medium py-6 border-b border-gray-200 mb-8">Control de Calidad & Envío</h3>
                    <div class="space-y-6">
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Inspección manual + máquina en nuestro almacén</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Opciones de transporte aéreo/marítimo, seguimiento en tiempo real</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">✓</span>
                            </div>
                            <span>Entrega directa en su dirección</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="text-center mt-12">
            <p class="mb-4 text-gray-600">点击按钮切换当前步骤，观察图片蒙层效果：</p>
            <div class="space-x-4">
                <button onclick="setActiveStep(0)" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">步骤 1</button>
                <button onclick="setActiveStep(1)" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">步骤 2</button>
                <button onclick="setActiveStep(2)" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">步骤 3</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1; // 默认第2步激活

        function setActiveStep(step) {
            currentStep = step;
            
            // 更新图片蒙层
            document.querySelectorAll('.step-image').forEach((img, index) => {
                const overlay = img.querySelector('.step-image-overlay');
                if (index === step) {
                    overlay.classList.add('hidden'); // 当前步骤隐藏蒙层
                } else {
                    overlay.classList.remove('hidden'); // 其他步骤显示蒙层
                }
            });
            
            // 更新步骤圆圈
            document.querySelectorAll('.step-circle').forEach((circle, index) => {
                const span = circle.querySelector('span');
                if (index === step) {
                    circle.classList.remove('border-gray-300');
                    circle.classList.add('border-red-500');
                    span.classList.remove('text-gray-500');
                    span.classList.add('text-red-500');
                } else {
                    circle.classList.remove('border-red-500');
                    circle.classList.add('border-gray-300');
                    span.classList.remove('text-red-500');
                    span.classList.add('text-gray-500');
                }
            });
        }

        // 初始化
        setActiveStep(currentStep);
    </script>
</body>
</html>
