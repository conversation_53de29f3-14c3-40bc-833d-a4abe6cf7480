<template>
  <div
    class="w-full min-h-screen bg-white px-[24px] pt-[140px] flex flex-col items-center"
  >
    <img
      src="@/assets/icons/find-goods/success.svg"
      alt="success"
      class="w-[104px] h-[104px]"
    />
    <div class="text-[26px] leading-[26px] font-medium mt-[30px]">
      Enviado con éxito
    </div>

    <div class="w-full h-[1px] bg-[#F2F2F2] my-[24px]"></div>
    <div class="text-[18px] leading-[22px] text-center">
      Regrese al punto de encuentro antes delas
      <span class="text-[20px] leading-[27px] text-[#F20] font-medium"
        >15:45</span
      >
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();

const pageData = reactive({
  keyword: "",
  isLoading: false,
});
</script>

<style scoped lang="scss">
:deep(.n-input .n-input__input-el) {
  height: 48px;
}
</style>
