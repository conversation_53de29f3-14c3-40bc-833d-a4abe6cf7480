syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics";

import "chilat/logistics/param/route_param.proto";
import "chilat/logistics/model/route_model.proto";
import "common.proto";

// 线路管理
service Route {
  // 保存线路
  rpc save (RouteSaveParam) returns (common.ApiResult);
  // 分页查询线路
  rpc pageList (RoutePageListParam) returns (RoutePageListResp);
  // 查询日志
  rpc listLog (common.IdParam) returns (RouteLogListResp);
  // 开启关闭
  rpc enabled (common.EnabledParam) returns (common.ApiResult);
  // 查询所有线路
  rpc listAll (common.EmptyParam) returns (RouteListResp);
  //根据countryId查询线路相关
  rpc listRoute(RouteFindByCountryParam) returns (CountryRouteListResp);
}