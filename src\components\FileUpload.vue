<template>
  <div>
    <n-upload
      :style="{ width: props.width }"
      :accept="props.accept"
      :max="props.max"
      :multiple="multiple"
      list-type="image-card"
      :file-list="pageData.fileList"
      @before-upload="onBeforeUpload"
      @remove="onUploadRemove"
      :custom-request="onCustomUpload"
    >
    </n-upload>
  </div>
</template>

<script setup lang="ts" name="FileUpload">
import { useMessage } from "naive-ui";
import type { UploadFileInfo } from "naive-ui";

const message = useMessage();
const emit = defineEmits(["uploadEvent"]);

const props = defineProps({
  max: {
    type: Number,
    default: 1,
  },
  width: {
    type: String,
    default: "100%",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: "image/*",
  },
  files: {
    type: Array,
    default: () => [],
  },
  spmCode: {
    type: String,
    default: "",
  },
});

// 添加标志位和批量上传状态
let isExternalUpdate = false;
let isBatchUploading = false;
let pendingFiles: UploadFileInfo[] = [];

onBeforeMount(() => {
  isExternalUpdate = true;
  initFileList(props.files);
});

// 修改 watch 监听
watch(
  () => props.files,
  (newFiles) => {
    if (isExternalUpdate) {
      // 如果不是批量上传过程中，才更新列表
      if (!isBatchUploading) {
        initFileList(newFiles);
      }
      isExternalUpdate = false;
    }
  },
  { deep: true }
);

const pageData = reactive({
  fileList: [] as UploadFileInfo[],
  uploading: false,
});

// 初始化文件列表
const initFileList = (files: any[]) => {
  if (files?.length) {
    pageData.fileList = files.map((item, index) => ({
      id: `file-${index}-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`,
      name: typeof item === "object" ? item.fileName : `file-${index}`,
      url: typeof item === "object" ? item.fileUrl : item,
      status: "finished",
      thumbnailUrl: typeof item === "object" ? item.fileUrl : item,
    }));
  } else {
    pageData.fileList = [];
  }
};

function onBeforeUpload(data: { file: UploadFileInfo }) {
  const { file } = data;
  if (!file.type?.startsWith("image/")) {
    return false;
  }
  if (file.file) {
    file.thumbnailUrl = URL.createObjectURL(file.file);
  }
  file.id = `file-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  // 添加到待上传列表
  pendingFiles.push(file);
  
  // 如果是批量上传的第一个文件，设置标志位
  if (pendingFiles.length > 1 && !isBatchUploading) {
    isBatchUploading = true;
  }
  
  // 添加到显示列表
  pageData.fileList.push(file);
  return true;
}

const onCustomUpload = async (options: {
  file: UploadFileInfo;
  onFinish: () => void;
  onError: (e?: Error) => void;
}) => {
  const { file, onFinish, onError } = options;
  try {
    pageData.uploading = true;

    const target = pageData.fileList.find((f) => f.id === file.id);
    if (target) {
      target.status = "uploading";
    }

    const formData = new FormData();
    formData.append("file", file.file as File);

    const BASEURL =
      typeof window === "object"
        ? useRuntimeConfig().public.clientURL
        : useRuntimeConfig().public.baseURL;

    const { data: response, error } = await useFetch(
      "/foundation/FileManager/uploadStream",
      {
        baseURL: BASEURL,
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${useAuthStore().token}`,
        },
        key: `upload-${file.id}-${Date.now()}`,
        cache: "no-cache",
      }
    );

    if (error.value) {
      throw error.value;
    }

    const res = response.value as any;
    if (res?.result?.code === 200) {
      const target = pageData.fileList.find((f) => f.id === file.id);
      if (target) {
        target.status = "finished";
        target.url = res.data;
        target.thumbnailUrl = res.data;
      }
      
      // 从待上传列表移除
      pendingFiles = pendingFiles.filter(f => f.id !== file.id);
      
      // 如果是批量上传的最后一张
      if (isBatchUploading && pendingFiles.length === 0) {
        isBatchUploading = false;
        emitFileList();
      } else if (!isBatchUploading) {
        // 单张上传立即触发更新
        emitFileList();
      }
      
      if (props.spmCode === "goods_find_upload_image") {
        window?.MyStat?.addPageEvent(
          "goods_find_upload_image",
          `上传找货商品图片：${res.data}`
        );
      }
      onFinish();
    } else {
      throw new Error(res?.result?.message || "服务器返回错误");
    }
  } catch (err: any) {
    const target = pageData.fileList.find((f) => f.id === file.id);
    if (target) {
      target.status = "error";
    }
    // 从待上传列表移除
    pendingFiles = pendingFiles.filter(f => f.id !== file.id);
    // 如果出错时还有待上传文件，取消批量上传状态
    if (isBatchUploading && pendingFiles.length === 0) {
      isBatchUploading = false;
    }
    message.error(`上传失败 ${file.name}: ${err.message || err}`);
    onError(err);
  } finally {
    pageData.uploading = false;
  }
};

const onUploadRemove = (data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
  index: number;
}) => {
  pageData.fileList = pageData.fileList.filter((f) => f.id !== data.file.id);
  // 同时从待上传列表移除
  pendingFiles = pendingFiles.filter(f => f.id !== data.file.id);
  emitFileList();
};

const emitFileList = () => {
  const finishedFiles = pageData.fileList.filter(
    (f) => f.status === "finished"
  );

  const files = finishedFiles.map((file) => ({
    fileName: file.name,
    fileUrl: file.url,
  }));

  // 设置标志位并触发更新
  isExternalUpdate = true;
  emit("uploadEvent", files);
};
</script>

<style scoped>
:deep(.n-upload-file-list) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.n-upload-file) {
  width: 96px !important;
  height: 96px !important;
}
</style>