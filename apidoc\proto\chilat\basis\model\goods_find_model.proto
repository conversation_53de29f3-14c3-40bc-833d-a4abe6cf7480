syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsFindListPageResp {
  common.Result result = 1;
  GoodsFindListPageModel data = 2;
}

message GoodsFindListPageModel {
  common.Page page = 1;
  repeated GoodsFindModel data =2;
  int32 waitAssignCount = 3; // 待分配数量
  int32 waitGoodsFindCount = 4; // 待找货数量
  int32 goodsFindingCount = 5; // 找货中数量
  int32 finishedCount = 6; // 已完成数量
}

message GoodsFindModel {
  string id = 10;
  string goodsFindNo = 20 ; // 找货单号
  string createTime = 30; // 找货时间 yyyy-MM-dd HH:mm:ss
  common.GoodsFindStatusEnum status = 40; // 状态
  string statusDesc = 41; // 状态描述
  string salesUserId = 50; // 负责人ID
  string salesUserName = 60; // 负责人名称
  bool isAcceptSimilarProduct = 70; // 是否接受相似商品
  int32 count = 80; // 找货总款数
  string whatsapp = 90; // WhatsApp
  string email = 100; // 邮箱
  string name = 110; // 找货人姓名
  string userName = 111; // 用户名
  string countryId = 120; // 国家ID
  string country = 121; // 国家
  string userRemark = 130; // 用户备注
  repeated string remarkList = 140; // 找货备注列表
  string newestRemark = 141; // 最新的找货备注
  bool isNotifyCustomer = 150; // 是否通知客户
}

message GoodsFindDetailResp {
  common.Result result = 1;
  GoodsFindDetailModel data = 2;
}

message GoodsFindDetailModel {
  GoodsFindModel baseInfo = 10; // 基本信息
  repeated GoodsFindLineModel goodsList = 20; // 商品信息
  repeated GoodsFindEditLogModel editLogList = 30; // 操作日志
}

message GoodsFindLineModel {
  string id = 1;
  repeated string imageList = 10; // 图片列表
  string goodsName = 20; // 商品名称
  string count = 30; // 数量
  bool isPriceLimited = 40;  // 是否限制价格
  double minPrice = 50; // 价格下限, isPriceLimited为false时不填，isPriceLimited为true时minPrice和maxPrice必须要至少填一个
  double maxPrice = 60; // 价格上限, isPriceLimited为false时不填，isPriceLimited为true时minPrice和maxPrice必须要至少填一个
}

message GoodsFindEditLogModel {
  string id = 1;
  string operatorTime = 10; // 操作时间
  string operator = 20; // 操作人
  string description = 30; // 操作内容
}

message RemarkListResp {
  common.Result result = 1;
  RemarkListModel data = 2;
}

message RemarkListModel {
  repeated RemarkModel remarkList = 10;
}

message RemarkModel {
  string id = 10;
  string createTime = 20; // 创建时间
  string salesManId = 30; // 创建人ID
  string salesManName = 40; // 创建人名称
  string remark = 50; // 备注
}