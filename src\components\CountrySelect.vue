<template>
  <!-- 弹窗模式 -->
  <n-modal
    v-if="mode === 'modal'"
    preset="card"
    v-model:show="showModal"
    :closable="false"
    :closeOnEsc="false"
    :maskClosable="false"
    title=""
    class="site-modal"
    style="width: 1030px"
  >
    <div class="flex mb-[24px]">
      <img
        src="@/assets/icons/deliveryAddress.svg"
        alt="country-select"
        class="w-[18px] mr-[12px]"
        loading="lazy"
        referrerpolicy="no-referrer"
      />
      <div class="text-[20px] font-medium">
        {{ authStore.i18n("cm_common.selectYourLocation") }}
      </div>
    </div>
    <div class="mb-[20px] flex flex-wrap min-h-[220px]">
      <n-radio-group
        v-model:value="selectedCountry"
        class="country-grid"
        @update:value="onSelectCountry"
      >
        <n-radio-button
          v-for="item in countryOptions"
          :key="item.value"
          :value="item.value"
          class="country-item"
        >
          <div class="country-content">
            <img
              :src="item.logo"
              alt="country-select"
              class="country-flag"
              loading="lazy"
              referrerpolicy="no-referrer"
            />
            <span class="country-name">{{ item.label }}</span>
          </div>
        </n-radio-button>
      </n-radio-group>
    </div>
    <div class="flex justify-between text-[16px] leading-[16px]">
      <div class="flex">
        <div>{{ authStore.i18n("cm_common.selectedCountry") }}</div>
        <div class="ml-[4px] text-[#e50113]">
          {{ pageData.selectedCountry?.label }}
        </div>
      </div>
      <div class="text-[#7F7F7F]">
        {{ authStore.i18n("cm_common.locationDesc") }}
      </div>
    </div>

    <div class="text-center mt-[18px]">
      <n-button
        type="primary"
        size="large"
        round
        block
        @click="onSaveCountry"
        class="h-[38px] text-[16px] leading-[16px]"
      >
        {{ authStore.i18n("cm_common.confirmLocation") }}
      </n-button>
    </div>
  </n-modal>

  <!-- 下拉模式 -->
  <n-popover
    v-else
    trigger="hover"
    placement="bottom"
    v-model:show="showPopover"
    :duration="isSelectingCountry ? 3000 : 200"
  >
    <template #trigger>
      <slot name="trigger">
        <div class="cursor-pointer flex items-center flex-wrap popover-country">
          <div class="text-[12px] leading-[16px] mr-[8px] country-delivery">
            {{ authStore.i18n("cm_common.deliveryTo") }}
          </div>
          <div
            class="flex items-center text-[14px] font-medium country-code"
            v-if="siteInfo?.logo"
          >
            <img
              alt="logo"
              class="w-[20px] h-[14px] mr-[4px]"
              :src="siteInfo.logo"
              loading="lazy"
              referrerpolicy="no-referrer"
            />
            <div>
              {{ siteInfo.code }}
            </div>
          </div>
        </div>
      </slot>
    </template>

    <div class="w-[280px] p-[12px]">
      <div class="mb-[14px] font-medium text-[16px] leading-[16px]">
        {{ authStore.i18n("cm_common.selectYourLocation") }}
      </div>
      <div class="mb-[20px] text-[14px] leading-[14px]">
        {{ authStore.i18n("cm_common.locationDesc") }}
      </div>
      <div>
        <n-select
          filterable
          :options="countryOptions"
          v-model:value="selectedCountry"
          @update:value="onSelectCountry"
          :render-label="renderCountryOption"
          :placeholder="authStore.i18n('cm_common.selectLocation')"
        />
      </div>
      <div class="mt-[16px] text-center">
        <n-button type="primary" round block @click="onSaveCountry">
          {{ authStore.i18n("cm_common.confirmLocation") }}
        </n-button>
      </div>
    </div>
  </n-popover>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { h, ref, computed, watch, onMounted } from "vue";
import { getSiteInfo, getSiteList, updateCurrentSite } from "@/utils/siteUtils";

const props = defineProps({
  mode: {
    type: String,
    default: "popover",
  },
  show: {
    type: Boolean,
    default: false,
  },
  spm: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:show", "save"]);
const message = useMessage();
const authStore = useAuthStore();
const showPopover = ref(false);
const isSelectingCountry = ref(false);
const pageData = reactive(<any>{
  selectedCountry: null,
});

// 控制弹窗显示
const showModal = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 站点选择相关数据
const selectedCountry = ref<string | number>("");
const siteInfo = computed(() => getSiteInfo());

// 处理国家列表选项
const countryOptions = computed(() => {
  const siteList = getSiteList();
  return siteList.map((site: any) => ({
    label: site.name,
    value: site.id,
    code: site.code,
    logo: site.logo,
  }));
});

// 自定义选项渲染函数，显示国旗和国家名称
const renderCountryOption = (option: any) => {
  if (!option) return null;

  return h("div", { class: "flex items-center" }, [
    h("img", {
      src: option.logo,
      alt: option.label,
      class: "w-[20px] h-[14px] mr-[8px]",
      style: "display: inline-block; vertical-align: middle;",
      loading: "lazy",
    }),
    h("span", { style: "vertical-align: middle;" }, option.label),
  ]);
};

// 初始化组件
// onMounted(() => {
//   if (siteInfo.value.id) {
//     selectedCountry.value = siteInfo.value.id;
//   }
// });

// 选择国家时更新选中值
const onSelectCountry = (value: string) => {
  isSelectingCountry.value = true;
  selectedCountry.value = value;
  pageData.selectedCountry = countryOptions.value.find(
    (item: any) => item.value === value
  );
  window?.MyStat?.addPageEvent(
    "select_site_click_one",
    `点击选择了站点：${pageData.selectedCountry?.code}`
  );
};

// 保存选择的国家
async function onSaveCountry() {
  if (!selectedCountry.value) return;
  const res: any = await useSetCurrentSite({
    id: selectedCountry.value,
  });
  if (res.result.code === 200) {
    updateCurrentSite(selectedCountry.value);
    // 触发保存事件
    emit("save", selectedCountry.value);
    const selectCode = countryOptions.value.find(
      (item: any) => item.value === selectedCountry.value
    )?.code;
    window?.MyStat?.addPageEvent(
      "select_site_changed",
      `当前站点已改变：${selectCode}`,
      true
    );

    // modal关闭
    if (props.mode === "modal") {
      showModal.value = false;
    } else {
      // popover关闭
      showPopover.value = false;
    }
  }
}

// 添加埋点事件
const onAddSiteSelectEvent = () => {
  let remark;
  if (props.spm === "select_site_from_nav") {
    remark = "在导航栏打开站点选择框";
  } else if (props.spm === "select_site_from_pop") {
    remark = "在弹窗中打开站点选择框";
  } else if (props.spm === "select_site_from_goods") {
    remark = "在商品信息中打开站点选择框";
  } else if (props.spm === "select_site_from_cart") {
    remark = "在购物车中打开站点选择框";
  }
  window?.MyStat?.addPageEvent(props.spm, remark);
};

// 监听showPopover变化
watch(showPopover, (newVal) => {
  if (newVal) {
    isSelectingCountry.value = false;
    selectedCountry.value = siteInfo.value.id || "";
    // 添加埋点
    onAddSiteSelectEvent();
  } else {
    window?.MyStat?.addPageEvent("select_site_close_dialog", `关闭站点选择框`);
  }
});

// 监听showModal变化
watch(
  showModal,
  (newVal) => {
    if (newVal) {
      // 添加埋点
      onAddSiteSelectEvent();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.country-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
  width: 100%;
}

.country-item {
  border: 1px solid #d9d9d9;
  border-radius: 500px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: #e50113;
    color: #e50113;
  }

  &.n-radio-button--checked {
    border-color: #e50113;
    color: #e50113;
  }
}

.country-content {
  width: 190px;
  display: flex;
  align-items: center;
  padding: 10px 12px;
  font-size: 14px;
  line-height: 14px;
}

.country-flag {
  width: 20px;
  height: 14px;
  margin-right: 6px;
  object-fit: cover;
}

.country-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.n-radio-button) {
  padding: 0;
}
:deep(.n-radio-group__splitor) {
  display: none !important;
}
:deep(.n-radio-group .n-radio-button) {
  --n-button-box-shadow-hover: none;
  --n-button-border-color-active: none;
  --n-button-box-shadow-focus: none;
  --n-button-box-shadow-focus-active: none;
  --n-button-box-shadow-focus-hover: none;
  --n-button-box-shadow-focus-active-hover: none;
}
:deep(.n-radio-group .n-radio-button:last-child) {
  border-top-right-radius: 500px;
  border-bottom-right-radius: 500px;
}
:deep(.n-radio-group .n-radio-button:first-child) {
  border-top-left-radius: 500px;
  border-bottom-left-radius: 500px;
}
</style>
