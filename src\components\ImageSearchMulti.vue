<template>
  <n-popover
    v-model:show="showImageSearch"
    trigger="click"
    :show-arrow="false"
    :raw="true"
    :placement="props.placement"
    class="image-search-popover"
  >
    <template #trigger>
      <div @click="onTriggerClick">
        <slot></slot>
      </div>
    </template>

    <div class="image-search-container">
      <div class="flex justify-center relative">
        <div class="text-[16px] text-[#222] mb-[22px] text-center font-medium">
          {{ authStore.i18n("cm_search.imageSearch") }}
        </div>
        <icon-card
          name="material-symbols:close-rounded"
          size="20"
          color="#333"
          @click="showImageSearch = false"
          class="absolute right-0 cursor-pointer"
        ></icon-card>
      </div>
      <div
        class="upload-area"
        :class="{ uploading: showLoading }"
        @dragover="onDragOver"
        @dragleave="onDragLeave"
        @drop="onDrap"
      >
        <div class="h-full p-[30px] flex items-center relative">
          <template v-if="!showLoading">
            <img
              loading="lazy"
              src="@/assets/icons/imageSearchMulti.svg"
              alt="upload"
              class="w-[64px] h-[64px] object-contain mr-[10px]"
              referrerpolicy="no-referrer"
            />
            <div class="text-left">
              <h3 class="text-[16px] text-[#222] mb-[20px] font-medium">
                {{ authStore.i18n("cm_search.imageSearchTitle") }}
              </h3>
              <div class="upload-methods">
                <p>
                  • {{ authStore.i18n("cm_search.pasteImage") }} <kbd>Ctrl</kbd
                  ><kbd>V</kbd>
                </p>
                <p>
                  • {{ authStore.i18n("cm_search.dragImage") }}
                  <n-button
                    text
                    type="primary"
                    @click.stop="onTriggerUpload"
                    class="text-[#e50113] text-[16px] underline"
                    >{{ authStore.i18n("cm_search.uploadFile") }}</n-button
                  >
                </p>
              </div>
            </div>
          </template>
          <template v-else>
            <n-spin size="large">
              <template #description>
                <p class="text-[20px] mt-[18px] font-bold text-[#666]">
                  {{ authStore.i18n("cm_search.uploading") }}
                </p>
              </template>
            </n-spin>
          </template>
        </div>
      </div>
      <input
        ref="fileInputRef"
        type="file"
        accept="image/*"
        style="display: none"
        @change="onFileSelect"
        data-spm-box="navigation-image-search"
      />
      <n-alert
        v-if="errorMessage"
        class="mt-[24px]"
        type="error"
        closable
        @close="errorMessage = ''"
        :bordered="false"
        :title="authStore.i18n('cm_search.uploadError')"
      >
        <div class="text-[14px] text-[#333] mt-[8px]">
          {{ errorMessage }}
        </div>
      </n-alert>
    </div>
  </n-popover>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useAuthStore } from "@/stores/authStore";
import { ImageCompressor } from "@/utils/imageCompressor";

const message = useMessage();
const authStore = useAuthStore();
const showImageSearch = ref(false);
const showLoading = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null);
const errorMessage = ref("");
const allowedTypes = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/bmp",
  "image/webp",
];

const props = defineProps({
  placement: {
    type: String,
    default: "bottom",
  },
});

function onTriggerClick() {
  errorMessage.value = "";
  showLoading.value = false;
  showImageSearch.value = true;
}

onMounted(() => {
  window.addEventListener("paste", onPaste);
});

onUnmounted(() => {
  window.removeEventListener("paste", onPaste);
});

function onValidateImage(file: File) {
  if (!allowedTypes.includes(file.type)) {
    errorMessage.value = authStore.i18n("cm_search.imageFormatError");
    return false;
  }
  return true;
}

function onTriggerUpload() {
  fileInputRef.value?.click();
}

async function onPaste(event: ClipboardEvent) {
  const items = event.clipboardData?.items;
  if (!items) return;

  for (const item of items) {
    if (item.type.indexOf("image") === 0) {
      const file = item.getAsFile();
      if (file) {
        await onProcessImage(file, "paste");
      }
      break;
    }
  }
}

function onDragOver(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();
}

function onDragLeave(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();
}

async function onDrap(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();
  const files = e.dataTransfer?.files;
  if (!files?.length) return;
  const file = files[0];
  if (file.type.startsWith("image/")) {
    await onProcessImage(file, "drag");
  }
}

async function onFileSelect(event: Event) {
  const input = event.target as HTMLInputElement;
  const files = input.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file.type.startsWith("image/")) {
      await onProcessImage(file, "file", event);
    }
  }
  input.value = "";
}

async function onProcessImage(file: File, uploadType?: any, event?: Event) {
  errorMessage.value = "";
  showLoading.value = true;

  const compressedFile = await ImageCompressor.compress(file, {
    maxSizeKB: 300,
    quality: 1,
    maxWidth: 500,
  });
  await onImageUploadSuccess(compressedFile, uploadType, event);
}

async function onImageUploadSuccess(
  file: File,
  uploadType?: any,
  event?: Event
) {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const BASEURL =
      typeof window == "object"
        ? useRuntimeConfig().public.clientURL
        : useRuntimeConfig().public.baseURL;

    const { data }: any = await useFetch(
      "/commodity/GoodsSearch/uploadImage1688",
      {
        baseURL: BASEURL,
        method: "POST",
        body: formData,
        cache: "no-cache",
      }
    );

    if (data?.value?.result?.code === 200) {
      const baseParams = {
        type: "imgSearch",
        imageId: data?.value?.data?.imageId,
        imageUrl: data?.value?.data?.imageUrl,
      };

      let params = { ...baseParams };
      if (uploadType === "paste") {
        params.spm = window.MyStat?.getSpmCode("copy-image-search");
      } else if (uploadType === "drag") {
        params.spm = window.MyStat?.getSpmCode("drag-image-search");
      }

      navigateToPage(
        "/goods/list/all",
        params,
        false,
        uploadType === "file" ? event : undefined
      );
    } else {
      errorMessage.value = data?.value?.result?.message;
    }
  } catch (error) {
    // errorMessage.value = authStore.i18n("cm_search.imageSearchError");
  } finally {
    showLoading.value = false;
  }
}
</script>

<style scoped lang="scss">
.image-search-popover {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

  :deep(.n-popover) {
    padding: 0;
    max-width: none;
  }
}

.image-search-container {
  width: 650px;
  min-height: 330px;
  background: #fff;
  border-radius: 8px;
  padding: 28px;
}

.upload-area {
  height: 230px;
  border: 1px dashed #dcdcdc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;

  &.uploading {
    border-color: #dcdcdc;
    background-color: rgba(250, 250, 250, 0.8);
    cursor: wait;
  }

  &:not(.uploading):hover {
    border-color: #999;
    background-color: #f5f5f5;
  }
}

.upload-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
  color: #222;
  font-size: 16px;

  p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  kbd {
    display: inline-flex;
    border: 1px solid #767676;
    border-radius: 2px;
    padding: 0 2px;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    margin-right: 2px;
    min-width: 24px;
  }
}
</style>
