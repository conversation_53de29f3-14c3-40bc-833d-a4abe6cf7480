<template>
  <div class="pb-[60px]">
    <div class="flex flex-col gap-[60px]">
      <category-carousal :selector="props.selectorList[0]"></category-carousal>
    </div>

    <div class="flex flex-col items-center justify-center mt-[40px] mb-[80px]">
      <img
        loading="lazy"
        src="@/assets/icons/home/<USER>"
        alt="Descubra su próxima oportunidad de negocio"
        referrerpolicy="no-referrer"
      />
      <div class="flex gap-[20px] pt-[60px]">
        <a
          target="_blank"
          class="banner-link"
          href="/selector?code=yiwu-index"
          data-spm-box="homepage-banner-yiwu"
        >
          <div class="relative inline-block">
            <img
              loading="lazy"
              src="@/assets/icons/home/<USER>"
              alt="Los productos populares del Mercado de Yiwu"
              class="rounded-[12px] relative z-2 transition-all duration-120"
              referrerpolicy="no-referrer"
            />
            <div class="banner-shadow"></div>
          </div>
        </a>
        <a
          target="_blank"
          class="banner-link"
          href="/selector?code=festival-index"
          data-spm-box="homepage-banner-festival"
        >
          <div class="relative inline-block">
            <img
              loading="lazy"
              src="@/assets/icons/home/<USER>"
              alt="Lista de los productos de fiesta más vendidos"
              class="rounded-[12px] relative z-2 transition-all duration-120"
              referrerpolicy="no-referrer"
            />
            <div class="banner-shadow"></div>
          </div>
        </a>
      </div>
    </div>

    <div class="my-[80px] bg-white relative">
      <img src="@/assets/icons/home/<USER>" alt="" />
      <div
        class="w-[560px] h-[438px] absolute top-0 right-0 pt-[20px] pb-[30px] text-[22px] leading-[22px] text-[#11263B] flex flex-col justify-between"
      >
        <div class="ml-auto">
          <div class="flex items-start">
            <img
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.quickPreciseSearch')"
            />
            <div>
              <div class="pt-[7px] pb-[32px] pl-[18px]">
                {{ authStore.i18n("cm_home.quickPreciseSearch") }}
              </div>
              <img alt="line" src="@/assets/icons/home/<USER>" />
            </div>
          </div>
          <div class="flex items-start mt-[33px]">
            <img
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.fullSpanishSupport')"
            />
            <div>
              <div class="pt-[3px] pb-[32px] pl-[18px]">
                {{ authStore.i18n("cm_home.fullSpanishSupport") }}
              </div>
              <img alt="line" src="@/assets/icons/home/<USER>" />
            </div>
          </div>
        </div>
        <a
          target="_blank"
          href="/goods/looking"
          class="custom-search mr-[20px]"
          data-spm-box="button-find-homepage-mid"
        >
          <img
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.guidedSearchInit')"
          />
          <div class="text-[28px] leading-[28px] font-medium">
            {{ authStore.i18n("cm_home.guidedSearchInit") }}
          </div>
          <img
            alt="arrow"
            class="arrow"
            src="@/assets/icons/common/arrow-right-white.svg"
          />
        </a>
      </div>
    </div>
    <div class="flex flex-col gap-[60px]">
      <category-carousal
        v-if="props.habitableCapsuleGoods?.goodsList?.length"
        :selector="props.habitableCapsuleGoods"
      ></category-carousal>
      <!-- “优质供应商”商品推荐 -->
      <category-carousal
        v-if="props.recommendSupplierGoods?.goodsList?.length"
        :selector="props.recommendSupplierGoods"
      ></category-carousal>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import CategoryCarousal from "./CategoryCarousal.vue";

const authStore = useAuthStore();

interface selectorItem {
  spmCode: string;
  name: string;
  tagId: string;
  goodsList: any[];
}

const props = defineProps({
  selectorList: {
    type: Array as () => selectorItem[],
    default: () => [],
  },
  recommendSupplierGoods: {
    type: Object,
    default: () => <any>{},
  },
  habitableCapsuleGoods: {
    type: Object,
    default: () => <any>{},
  },
});
</script>

<style scoped lang="scss">
.banner-link {
  &:hover {
    img {
      transform: translateY(-12px);
    }
  }
}

.banner-shadow {
  width: calc(100% - 48px);
  position: absolute;
  top: -12px;
  right: 0;
  left: 0;
  margin: 0 auto;
  height: 100%;
  background-color: #d8d8d8;
  border-radius: 12px;
  z-index: 1;
}
.custom-search {
  margin-top: auto;
  display: inline-flex;
  padding: 15px 24px 15px 32px;
  align-items: center;
  gap: 16px;
  border-radius: 50px 12px;
  background: #11263b;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid #11263b;
  position: relative;

  img,
  div {
    transition: transform 0.3s ease;
    will-change: transform;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &:hover {
    border: 2px solid #0080ff;
    color: #0080ff;
    background: transparent;
    transform: scaleY(1.3);

    img,
    div {
      transform: scaleY(0.769230769);
    }

    .arrow {
      content: url("@/assets/icons/home/<USER>");
    }
  }
}
</style>
