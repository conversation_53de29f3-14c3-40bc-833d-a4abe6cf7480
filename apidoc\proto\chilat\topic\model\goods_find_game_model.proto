syntax = "proto3";
package chilat.topic;

option java_package = "com.chilat.rpc.topic.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsFindGameListResp {
  common.Result result = 1;
  repeated GoodsFindGameModel data = 2;
}

message GoodsFindGameModel {
  string id = 10;
  repeated string imageUrls = 30;
  string startTime = 40; // 开始时间 yyyy-MM-dd HH:mm:ss
  string endTime = 50; // 结束时间 yyyy-MM-dd HH:mm:ss
  string url = 60; // 寻货链接
}

message GoodsFindGameDetailResp {
  common.Result result = 1;
  repeated GoodsFineGameQuestionModel data = 2;
}

message GoodsFineGameQuestionModel {
  string id = 10;
  string imageUrl = 20; // 图片url
  string specDesc = 30; // 参数-规格描述
}
