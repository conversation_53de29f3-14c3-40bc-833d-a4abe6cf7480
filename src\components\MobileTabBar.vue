<template>
  <div class="footer-bar">
    <div class="bar-content">
      <div
        v-for="(bar, index) in pageData.naiveBarList"
        :key="index"
        class="bar-item"
        :class="index === 2 ? '-mt-[0.28rem]' : 'w-[1.2rem]'"
        @click="onNaiveBar(index, bar, $event)"
        :data-spm-box="bar.spmCode"
      >
        <div v-if="index === 2" class="flex flex-col items-center">
          <div class="w-[1.04rem] h-[1.04rem] rounded-full center-icon">
            <div
              class="w-[0.76rem] h-[0.76rem] mx-auto rounded-full bg-[#e50113] mt-[0.08rem] flex items-center justify-center relative z-10"
            >
              <img
                class="w-[0.36rem] h-[0.36rem]"
                :alt="bar.name"
                :src="bar.icon"
              />
            </div>
          </div>
          <div
            class="text-[#333] mt-[-0.12rem] text-[0.24rem] leading-[0.24rem] text-center"
          >
            {{ authStore.i18n(bar.name) }}
          </div>
          <img
            alt="CHILATSHOP"
            class="w-[1.28rem] mt-[0.08rem] h-auto"
            src="@/assets/icons/common/logo.png"
          />
        </div>
        <template v-else>
          <!-- 不需要登录 -->
          <a v-if="!bar.needLogin" :href="bar.path" class="item-inner">
            <img
              :alt="bar.name"
              class="bar-icon"
              :src="pageData.naiveBar === index ? bar.iconActive : bar.icon"
            />
            <div
              class="mt-[0.04rem] text-[0.24rem] leading-[0.24rem]"
              :class="
                pageData.naiveBar === index ? 'text-[#333]' : 'text-[#7F7F7F]'
              "
            >
              {{ authStore.i18n(bar.name) }}
            </div>
            <img
              v-if="bar.path === '/h5/user' && !userInfo?.username"
              class="absolute z-2 w-[1.08rem] right-[-0.28rem] top-[-0.26rem]"
              src="@/assets/icons/gift.webp"
            />
          </a>

          <!-- 需要登录 -->
          <span v-else class="item-inner">
            <img
              :alt="bar.name"
              class="bar-icon"
              :src="pageData.naiveBar === index ? bar.iconActive : bar.icon"
            />
            <div
              class="mt-[0.04rem] text-[0.24rem] leading-[0.24rem]"
              :class="
                pageData.naiveBar === index ? 'text-[#333]' : 'text-[#7F7F7F]'
              "
            >
              {{ authStore.i18n(bar.name) }}
            </div>
            <img
              v-if="bar.path === '/h5/user' && !userInfo?.username"
              class="absolute z-2 w-[1.08rem] right-[-0.28rem] top-[-0.26rem]"
              src="@/assets/icons/gift.webp"
            />
          </span>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="MobileFooter">
import mobileHome from "@/assets/icons/common/mobile-home.svg";
import mobileHomeActive from "@/assets/icons/common/mobile-home-active.svg";
import mobileCategory from "@/assets/icons/common/mobile-category.svg";
import mobileCategoryActive from "@/assets/icons/common/mobile-category-active.svg";
import mobileCart from "@/assets/icons/common/mobile-cart.svg";
import mobileCartActive from "@/assets/icons/common/mobile-cart-active.svg";
import mobileUser from "@/assets/icons/common/mobile-user.svg";
import mobileUserActive from "@/assets/icons/common/mobile-user-active.svg";
import mobileFind from "@/assets/icons/common/mobile-find.svg";

import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
const config = useRuntimeConfig();
const route = useRoute();

const props = defineProps({
  naiveBar: {
    type: Number,
    default: 0,
  },
});
const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  naiveBar: props.naiveBar,
  naiveBarList: <any>[
    {
      icon: mobileHome,
      iconActive: mobileHomeActive,
      needLogin: false,
      path: "/h5",
      name: "cm_bar.home",
      spmCode: "navigation-bottom-home",
    },
    {
      icon: mobileCategory,
      iconActive: mobileCategoryActive,
      needLogin: false,
      path: "/h5/category",
      name: "cm_bar.category",
      spmCode: "navigation-bottom-category",
    },
    {
      icon: mobileFind,
      name: "cm_bar.find",
      needLogin: true,
      path: `/h5/search/looking`,
      spmCode: "button-find-bottom-mid",
    },
    {
      icon: mobileCart,
      iconActive: mobileCartActive,
      name: "cm_bar.list",
      needLogin: true,
      path: "/h5/find",
      spmCode: "navigation-bottom-cart",
    },
    {
      icon: mobileUser,
      iconActive: mobileUserActive,
      needLogin: false,
      path: "/h5/user",
      name: "cm_bar.account",
      spmCode: "navigation-bottom-myhome",
    },
  ],
});

function onNaiveBar(index: number, bar: any, event: MouseEvent) {
  pageData.naiveBar = index;
  if (!bar.needLogin) return;
  if (bar.needLogin && isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: bar.path }, false, event);
  } else {
    navigateToPage(bar.path, {}, false, event);
  }
}
</script>

<style scoped lang="scss">
.footer-bar {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding-top: 0.08rem;
  padding-bottom: 0.1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(0.5rem);
  z-index: 100;

  .bar-content {
    display: flex;
    justify-content: space-around;
    align-items: start;
    position: relative;
  }

  .bar-item {
    // flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 0.24rem;
    color: #7f7f7f;

    .item-inner {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .bar-icon {
      width: 0.52rem;
      height: 0.52rem;
    }
  }
  .center-icon {
    position: relative;
    overflow: hidden;
    z-index: -1;
  }
  .center-icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0.28rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(0.5rem);
    border-radius: 199.98rem;
  }
}
</style>
