syntax = "proto3";
package mall.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "common/business.proto";
import "mall/commodity/model/category_info_model.proto";
import "chilat/commodity/commodity_common.proto";


message GoodsInfoDetailResp {
    common.Result result = 1;
    GoodsInfoModel data = 2;
}

// 商品信息
message GoodsInfoModel {
    string id = 10; //商品ID
    string goodsNo = 20; // 商品编号
    string goodsName = 30; // 商品名称
    string goodsTitle = 40; // 商品标题
    bool isOnline = 50; //是否已上架（商品预览功能可包含未上架商品）
    string goodsPriceUnitName = 60; // 价格单位
    common.CurrencyType currency = 70; // 价格对应的货币
    double minPrice = 80; // 最低价
    double maxPrice = 90; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
    int32 minBuyQuantity = 100; //最小购买数量（起订量）
    int32 minIncreaseQuantity = 110; //最小加购数量（一次加购数量）
    string coverImage = 120; // 视频封面图片（若非空，则在商详页优先显示视频）
    string videoUrl = 130; // 视频
    repeated string goodsImageList = 140; // 商品图片组-附属属性
    double goodsLength = 150; // 商品长（单位：cm）
    double goodsWidth = 160; // 商品宽（单位：cm）
    double goodsHeight = 170; // 商品高（单位：cm）
    double goodsWeight = 180; // 商品重量-附属属性
    string goodsDesc = 190; // 商品描述（根据访问设备参数，自动取值PC或H5版的描述）
    repeated GoodsPriceRange goodsPriceRanges = 195; // 商品阶梯价范围（上架商品至少存在一个阶梯价范围）
    repeated chilat.commodity.GoodsAttrModel attrList = 200; // 属性参数-附属属性
    repeated GoodsSkuSpecModel specList = 210; // 商品规格列表（必填）
    repeated GoodsSkuInfoModel skuList = 220; // SKU列表（必填）
    double boxEstimateFreight = 230; // 单箱预估运费（按商品维度最小装箱数计算）
    int32 boxFreightGoodsQty = 235; // 参与单箱预估运费的商品数量
    double pcsEstimateFreight = 236; // 单件预估运费（1件的预估运费）
    string routeId = 240; //购物车中选中的线路ID
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
}

// 商品阶梯价范围（所有字段必填）
message GoodsPriceRange {
    int32 start = 10; // 阶梯价起始值（从 minBuyQuantity 开始）
    int32 end = 20; // 阶梯价结束值（-1表示不限）
    double minPrice = 30; // 最低价
    double maxPrice = 40; // 最高价（最低价与最高价相同时，前段仅显示一个价格）
}

// 商品SKU规格
message GoodsSkuSpecModel {
    string id = 10; //规格ID（必填）
    string name = 20; //规格名称（必填）
    repeated GoodsSkuSpecItemModel items = 30; //规格明细（必填）
}

// 商品SKU规格明细（含库存数量）
message GoodsSkuSpecItemModel {
    string itemId = 10; // 规格明细ID（必填）
    string itemName = 20; // 规格明细名称（如颜色规格中的“红色”，必填）
    int32 stockQty = 30; // 相关SKU总库存数量（必填）
    string imageUrl = 40; // 图片链接（例：SKU图片）
    string color = 50; // 颜色值（格式：# + 6位16进制数，例：#CCCCCC）
    int32 cartQty = 60; //相关SKU在购物车中的总数量（大于0传值，仅在第1个规格项传值）
}

// 商品SKU信息
message GoodsSkuInfoModel {
    string id = 10; // SKU ID（必填）
    string skuNo = 20; // SKU商品料号（必填）
    int32 stockQty = 30; //库存数量（必填）
    repeated GoodsSkuSpecIdPairModel specItems = 40; //SKU规格ID组：规格ID，规格明细ID
    repeated common.SkuStepPrice stepPrices = 50; // 阶梯价（价格单位在商品级别描述；“end=-1”表示不限）
    int32 cartQty = 60; //购物车中的数量（必填，默认0）
    int32 minIncreaseQuantity = 70; //最小加购数量（一次加购数量）；若此值非空且大于0，表示在SKU维度配置了“加购数量”
}

// 商品SKU中的规格ID与规格明细ID组合（通过检索SKU与反查有SKU规格）
message GoodsSkuSpecIdPairModel {
    string itemId = 10;     // 规格明细ID（当前商品内唯一）
    string itemName= 11;    // 规格项名称
    string specId = 20;     // 规格ID
}

//message GoodsCategoryModel {
//    string id = 1; // 商品分类ID
//    string name = 2; // 商品分类名称
//    repeated GoodsCategoryModel children = 4; // 下级商品分类
//}

message GoodsListDataItemModel {
    string goodsId = 10; // 商品ID
    string goodsNo = 20; // 商品编码
    string goodsName = 30; // 商品名称
    string goodsTitle = 40; // 商品标题
    string goodsPriceUnitName = 45; // 价格单位
    common.CurrencyType currency = 46; // 价格对应的货币
    double minPrice = 50; // 最低价
    double maxPrice = 55; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
    int32 minBuyQuantity = 110; //最小购买数量（起订量）
    int32 minIncreaseQuantity = 120; //最小加购数量（一次加购数量）
    string mainImageUrl = 60; // 商品主图
    float goodsRank = 210; //商品权重
    float hitScore = 220; // 搜索相关度得分
    string sourceGoodsId = 230; // 源商品ID
    double boxEstimateFreight = 240; // 单箱预估运费（按商品维度最小装箱数计算）
    int32 boxFreightGoodsQty = 245; // 参与单箱预估运费的商品数量
    double pcsEstimateFreight = 246; // 单件预估运费（1件的预估运费）
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
}

//推荐商品对象
message RecommendGoodsResultModel {
    repeated GoodsListDataItemModel goodsList = 10; //商品列表
    string tagId = 20; //标签ID
}

message SkuModel {
    string skuId = 10; //skuId
    string goodsId = 11; //goodsId
    string skuNo = 12; //skuNo
    string goodsNo = 13; //skuNo
    string picUrl = 20; //图片url
    string goodsName = 30; //商品名称
    repeated SpecModel specList = 40; //规格
    int32 count = 50; //数量
    string goodsPriceUnitName = 60; //单位
    double unitPrice = 70; //单价
    string currency = 80; //币种
    double totalAmount = 90; //总价
    string padc = 88; //促销活动动态代码
    string paName = 89; //促销活动名称（活动显示名称）
}

message SpecModel {
    string specName = 10; //规格名称 如color
    string itemName = 20; //具体规则明细 如red
}
