syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

message MidGoodsInfoResp {
  common.Result result = 1;
  MidGoodsInfoModel data = 2;
}

message MidGoodsInfoModel {
  string id = 10; //商品ID
  string goodsNo = 20; // 商品编号-主数据
  string brandName = 30; // 品牌名称
  repeated MidCategoryPathItemModel categoryPath = 40; //商品分类路径
  string goodsName = 50; // 商品名称-主数据
  string goodsTitle = 60; // 商品标题-主数据
  bool isOnline = 70; //是否已上架（商品预览功能可包含未上架商品）
  common.CurrencyType currency = 75; // 价格对应的货币
  string goodsPriceUnitName = 80; // 商品计价单位名称
  int32 packageInsideCount = 90; // 包装内含货品的数量
  string packageInsideUnit = 100; // 包装内含货品的数量单位
  int32 minBuyQuantity = 105; //最小购买数量（起订量）
  int32 minIncreaseQuantity = 106; //最小加购数量（一次加购数量）
  string coverImage = 110; // 视频封面图片（若非空，则在商详页优先显示视频）
  string videoUrl = 120; // 视频
  repeated string goodsImageList = 130; // 商品图片组
  double goodsLength = 140; // 商品长（单位：cm）
  double goodsWidth = 150; // 商品宽（单位：cm）
  double goodsHeight = 160; // 商品高（单位：cm）
  double goodsWeight = 170; // 商品重量
  string freightTips = 180; // 运费提示
  string goodsBarcode = 190; // 商家条形码
  string goodsPcDesc = 200; // PC商品描述
  string goodsH5Desc = 210; // H5商品描述
  repeated common.SkuStepRange  skuStepRanges = 230; // SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同）
  repeated commodity.GoodsAttrModel attrList = 240; // 属性参数（属性名称，属性值）
  repeated MidSkuSpecModel specList = 250; // 商品规格列表（仅包含有对应SKU的规格）
  repeated MidSkuInfoModel skuList = 260; // 商品SKU列表
  bool isAudit = 270; // 是否校验
  bool isIllegal = 280; // 是否侵权
  double boxEstimateFreight = 290; // 单箱预估运费（按商品维度最小装箱数计算）
  int32 boxFreightGoodsQty = 295; // 参与单箱预估运费的商品数量
  double pcsEstimateFreight = 296; // 单件预估运费（1件的预估运费）
  string padc = 88; //促销活动动态代码
  string paName = 89; //促销活动名称（活动显示名称）
}

// 商品SKU规格
message MidSkuSpecModel {
  string id = 10; // 规格ID（必填）
  string name = 20; // 规格名称（必填）
  repeated MidSkuSpecItemModel items = 30; // 规格明细（必填）
}

message MidSkuSpecItemModel {
  string itemId = 10; // 规格明细ID（必填）
  string itemName = 20; // 规格明细名称（如颜色规格中的“红色”，必填）
  string imageUrl = 30; // 图片链接（例：SKU图片）
  string color = 40; // 颜色值（格式：# + 6位16进制数，例：#CCCCCC）
}

// 商品SKU信息
message MidSkuInfoModel {
  string id = 10; // SKU ID（必填）
  string skuNo = 20; // SKU商品料号（必填）
  double salePrice = 30; //销售价
  int32 minIncreaseQuantity = 40; //最小加购数量（SKU维度一次加购数量，若值为null，则表示取SPU的minIncreaseQuantity）
  repeated MidSkuSpecIdPairModel specItems = 50; // SKU规格ID组：规格ID，规格明细ID
  repeated common.SkuStepPrice stepPrices = 60; // sku阶梯价格（件数从少到多，价格从高到低）
//  int32 stockQty = 70; //库存数量（必填）
  MidSkuStockModel stockInfo = 70; // 库存信息
  string imageUrl = 80; //SKU图片链接
  bool isOnline = 90; //是否上架
}

// 商品库存信息
message MidSkuStockModel {
  string id = 10; //库存ID
  int32 salesQty = 20; // 销售数量（含未付订单，不含取消单）
  int32 realQty = 30; // 实有库存数（可用库存数）
  int32 freezeQty = 40; // 冻结库存数
  int32 availableQty = 50; // 可用库存数
}

// 商品SKU中的规格ID与规格明细ID组合（通过检索SKU与反查有SKU规格）
message MidSkuSpecIdPairModel {
  string specId = 10; // 规格ID
  string specName = 20; // 规格名称
  string specNameCn = 30; // 规格中文名称
  string itemId = 40; // 规格明细ID（当前商品内唯一）
  string itemName = 50; // 规格明细名称
  string itemNameCn = 60; // 规格明细中文名称
}

message MidCategoryPathItemModel {
  string id = 10; // 商品分类ID
  string name = 20; // 商品分类名称
}

// ----------------------      商品分类数据     ----------------------
message MidCategoryTreeResp {
  common.Result result = 1;
  MidCategoryModel data = 2;
}

message MidCategoryModel {
  string id = 1; // 商品分类ID
  string name = 2; // 商品分类名称
  string cateLogo = 3; // 商品分类logo
  string cateIcon = 4; // 商品分类icon
  repeated MidCategoryModel children = 5; // 下级商品分类
}

/*
message MidGoodsPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated MidGoodsModel data = 3;
}

message MidGoodsModel {
  string id = 1; //id
  string goodsImageShow = 2; //商品主图
  string goodsName = 3; //商品名称
  string goodsNo = 4; //商品编码
  double minPrice = 5; //商品最低价格
  double minOldPrice = 6; // 商品最低原始价格
  string brandId = 7; //品牌id
  string brandName = 8; //品牌名称
  string categoryId = 9; //分类id
  string categoryName = 10; //分类名称
  string categoryId2th = 11; //二级类目id
  string categoryName2th = 12; //二级类目名称
  string categoryId1th = 13; //一级类目id
  string categoryName1th = 14; //一级类目名称
  string goodsImage = 15; //商品图片组
  double goodsWeight = 16; //商品重量
  int32 minBuyQuantity = 17; //起订量（最小购买数量）
  int32 stockQuantity = 18; //库存数量
  string goodsTitle = 19;
  string goodsPriceUnitId = 20;
  string goodsPriceUnitName = 21;
  double taxRate = 22;
  repeated MidSkuModel skuList = 25; //SKU信息
  double minSalesPrice = 26; //最低销售价
  double minOldSalesPrice = 27; //最低原始销售价
  MidSkuModel defaultSku = 28; //默认Sku
  int32 sales = 29; //商品销量
}

message MidSkuModel {
  string skuId = 1; //SKU ID
  string skuNo = 2; //SKU号
  string skuName = 3; //sku名称
  double minPrice = 4; //最优价格 阶梯价格/最小包裹数量
  int32 packageCount = 5; //最小销售包装的内含数量
  string packageUnit = 6; //最小销售包装单位
  string deliveryDate = 7; //交期
  string image = 8; //商品图片
  MidSkuPriceModel priceObj = 9; //商品会员价格
  repeated MidSkuSpecIdPairModel specList = 10; //规格
  int32 skuStockQuantity = 11; //库存数量
  repeated common.SkuStepPrice goodsPrice = 12; // sku阶梯价格（件数从少到多，价格从高到低）
}

message MidSkuPriceModel {
  double salePrice = 1; //商品售价
  double oldPrice = 2; //商品原价
  int32 oldPriceShow = 3; //是否显示原价1：显示，0：不显示
  double costPrice = 4; //商品成本价
  string stepPrice = 5; //商品阶梯价
  int32 memPriceShow = 6; //平台与供应商结算价格
  double supplierSettlePrice = 7; //平台-供应商结算单价
}
*/

//计算阶梯价返回结果
message MidCalcStepPriceResp {
  common.Result result = 1;
  map<string, MidCalcStepPriceModel> data = 2;
}
//计算阶梯价返回对象
message MidCalcStepPriceModel {
  string skuId = 10;
  double salePrice = 20; //销售价（原价）
  double stepPrice = 30; //阶梯价
}

// ----------------------      商品库存信息数据     ----------------------

// 按商品汇总库存返回结果
message MidGoodsStockSummaryResp {
  common.Result result = 1;
  map<string, MidStockSummaryModel> data = 2; // KEY 为 Goods ID
}

// 按商品汇总库存信息
message MidStockSummaryModel {
  int32 salesQty = 10; // 销量（不含取消单）
  int32 realStockQty = 20; // 实有库存数
  int32 availableStockQty = 30; // 可用库存数
}

// ----------------------      商品字典数据     ----------------------


// ----------------------      商品搜索结果     ----------------------

message MidSearchResultResp {
  common.Result result = 1;
  MidSearchResultModel data = 2;
}

message MidSearchResultModel {
  common.Page page = 10; //商品列表的分页信息
  repeated MidSearchGoodsModel goodsList = 20; //商品列表
  repeated MidSearchCategoryFilterModel mallCategoryFilters = 30; //商城前台商品分类过滤选项
  string tokenizeWords = 40; //搜索关键字的分词价格（用空格分隔）
}

message MidSearchGoodsModel {
  string goodsId = 10; // 商品ID
  string goodsNo = 20; // 商品编码
  string goodsName = 30; // 商品名称
  string goodsTitle = 40; // 商品标题
  string goodsPriceUnitName = 45; // 价格单位
  common.CurrencyType currency = 46; // 价格对应的货币
  double minPrice = 50; // 最低价
  double maxPrice = 55; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
  int32 minBuyQuantity = 60; //最小购买数量（起订量）
  int32 minIncreaseQuantity = 70; //最小加购数量（一次加购数量）
  string mainImageUrl = 80; // 商品主图
  string backendCategoryName = 90; // admin后台商品分类名称
  float goodsRank = 210; //商品权重
  float hitScore = 220; // 搜索相关度得分
  double boxEstimateFreight = 230; // 单箱预估运费（按商品维度最小装箱数计算）
  int32 boxFreightGoodsQty = 235; // 参与单箱预估运费的商品数量
  double pcsEstimateFreight = 236; // 单件预估运费（1件的预估运费）
  string padc = 88; //促销活动动态代码
  string paName = 89; //促销活动名称（活动显示名称）
}

//商品列表中商品分类过滤选项
message MidSearchCategoryFilterModel {
  string id = 10; //商城前台商品分类ID
  string name = 20; //商城前台商品分类名称
  int32 goodsCount = 25; //商品数量
  repeated MidSearchCategoryFilterModel children = 30; //下级分类过滤项（null表示不存在）
}

// 商城前台商品分类树
message MallCategoryTreeModel {
  string id = 1; // 商品分类ID
  string cateName = 2; // 商品分类名称
  string cateLogo = 3; // 商品分类logo
//    string cateIcon = 4; // 商品分类icon
  int32 goodsCount = 4; //商品数量（通过营销分类搜索得到）
  repeated MallCategoryTreeModel children = 5; // 下级商品分类
}

// 商城前台商品分类路径明细
message MallCategoryPathItemModel {
  string id = 10; // 商品分类ID
  string name = 20; // 商品分类名称
}

// ----------------------      ES商品索引更新     ----------------------

//ES商品索引重建
message RecreateESIndexResp {
  common.Result result = 1;
  RecreateESIndexModel data = 2;
}
message RecreateESIndexModel {
  int32 updateCount = 10;
  string remark = 20;
}

//更新全部商品的ES索引
message UpdateAllGoodsESIndexResp {
  common.Result result = 1;
  UpdateAllGoodsESIndexModel data = 2;
}
message UpdateAllGoodsESIndexModel {
  int32 updateCount = 10;
  string remark = 20;
}

message HomePageGoodsResp {
  common.Result result = 1;
  repeated HomePageGoodsModel data = 130; //首页商品
}


message HomePageGoodsModel {
  string categoryId = 1; //类目ID
  string categoryName = 2; //类目名称
  repeated MidSearchGoodsModel goodsList = 3; //商品
}

message MidRecommendGoodsResp {
  common.Result result = 1;
  repeated MidSearchGoodsModel data = 2; //商品
}

message HomePageCategoryResp {
  common.Result result = 1;
  repeated MidCategoryModel data = 2;
}

message PcHomePageGoodsResp {
  common.Result result = 1;
  repeated PcHomePageGoodsModel data = 2;
}

message PcHomePageGoodsModel {
  string categoryId = 1; //类目ID
  string categoryName = 2; //类目名称
  string image = 3; //图片
  string cateColor = 4; //颜色
  string subCateColor = 5; //子类目颜色
  repeated HomePageGoodsModel pageGoods = 6; //分类
}

message MidRecommendGoodsV2Resp {
  common.Result result = 1;
  MidRecommendGoodsV2Model data = 2;
}

message MidRecommendGoodsV2Model {
  //repeated MidSearchGoodsModel recommendGoods = 1;
  repeated HotSaleGoodsModel mercadoHotSaleGoods = 2; //美客多热销
  repeated HotSaleGoodsModel yiwuHotSaleGoods = 3; //义乌热销
  int32 yiwuTotalCount = 4;
  MidSearchGoodsModel hotSaleGoods = 5;
  HotSaleGoodsModel h5MercadoHotSaleGoods = 6; //h5美客多热销
  HotSaleGoodsModel h5YiwuHotSaleGoods = 7; //h5义乌热销
  HotSaleGoodsModel h5BackSchoolHotSaleGoods = 10; //开学季热销
  HotSaleGoodsModel h5CameraHotSaleGoods = 20; //摄像头热销
  HotSaleGoodsModel h5HumidifierHotSaleGoods = 30; //反重力加湿器热销
  repeated PcTagPageGoodsModel tagGoodsList = 40; //目前包含pc开学季热销,没有系统内的类目id,有tagid,其他跟PcHomePageGoodsModel一样
}

message MidRecommendCustomerGoodsResp {
  common.Result result = 1;
  MidRecommendCustomerGoodsModel data = 2;
}

message MidRecommendCustomerGoodsModel {
  repeated CustomerExclusiveActivityGoodsModel customerExclusiveActivities = 50; //客户专属活动列表（无专属活动，值为null；可有多个专属活动）
}

message HotSaleGoodsModel {
  string categoryId = 1; //类目ID
  string categoryName = 2; //类目名称
  string tagId = 3; //标签id
  repeated MidSearchGoodsModel hotSaleGoods = 4; //商品信息
  int64 totalCount = 5; //总数
}

//客户专属活动商品推荐（仅登录用户）
message CustomerExclusiveActivityGoodsModel {
  string title = 10; //活动标题（活动显示名称）
  repeated MidSearchGoodsModel goodsList = 20; //推荐商品列表
  string padc = 88; //促销活动动态代码（点击更多，进入padc商品查询页）
}

//更新全部商品vector
message UpdateAllGoodsVectorResp {
  common.Result result = 1;
  UpdateAllGoodsVectorModel data = 2;
}
message UpdateAllGoodsVectorModel {
  int64 updateCount = 10;
  string remark = 20;
}

message PcTagPageGoodsModel {
  string tagId = 1; //tagID
  string categoryName = 2; //类目名称
  string image = 3; //图片
  string cateColor = 4; //颜色
  string subCateColor = 5; //子类目颜色
  repeated HomePageGoodsModel pageGoods = 6; //分类
}

message GoodsVectorStatResp {
  common.Result result = 1;
  GoodsVectorStatRespModel data = 2;
}
message GoodsVectorStatRespModel {
  int64 countInMilvus = 10;
  int64 countInEs = 20;
}

