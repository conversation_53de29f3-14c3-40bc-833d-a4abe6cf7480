<template>
  <div class="page-footer" v-show="showFooter" id="page-footer">
    <div :style="{ width: footerWidth }" class="px-[40px]">
      <div class="footer-box" data-spm-box="navigation-bottom-article">
        <div class="box-wrapper">
          <p class="other-title">{{ authStore.i18n("cm_column.contactUs") }}</p>
          <div class="box-span !block"><EMAIL></div>
          <n-button
            color="#e50113"
            @click="onLoginClick(0)"
            v-if="!userInfo?.username"
          >
            {{ authStore.i18n("cm_common_registerNow") }}
          </n-button>
          <n-button
            v-else
            color="#e50113"
            @click="onGoAccount($event)"
            data-spm-box="navigation-bottom-myhome"
          >
            {{ authStore.i18n("cm_common_myAccount") }}
          </n-button>
        </div>
        <div class="box-wrapper">
          <p class="box-title">{{ authStore.i18n("cm_column.aboutUs") }}</p>
          <a class="box-span" target="_blank" href="/article/about-us">{{
            authStore.i18n("cm_news.aboutUs")
          }}</a>
          <a
            class="box-span"
            target="_blank"
            href="/article/frequently-questions"
            >{{ authStore.i18n("cm_news.askedQuestions") }}
          </a>
          <a class="box-span" target="_blank" href="/blog">{{
            authStore.i18n("cm_news.blog")
          }}</a>
          <a class="box-span" target="_blank" href="/article/invite">{{
            authStore.i18n("cm_news.invitedReward")
          }}</a>
          <a
            class="box-span"
            target="_blank"
            href="/tiendas-panoramicas-en-3d"
            >{{ authStore.i18n("cm_news.yiwu3DMarketMap") }}</a
          >
        </div>
        <div class="box-wrapper">
          <p class="box-title">
            {{ authStore.i18n("cm_column.companyPolicy") }}
          </p>
          <a class="box-span" target="_blank" href="/article/commission">{{
            authStore.i18n("cm_news.commission")
          }}</a>
          <a class="box-span" target="_blank" href="/article?code=10002">{{
            authStore.i18n("cm_news.warrantyService")
          }}</a>
          <a class="box-span" target="_blank" href="/article?code=10001">{{
            authStore.i18n("cm_news.privacyPolicy")
          }}</a>
          <a class="box-span" target="_blank" href="/article?code=10004">{{
            authStore.i18n("cm_news.termsOfService")
          }}</a>
        </div>
        <div class="box-wrapper">
          <p class="box-title">{{ authStore.i18n("cm_column.help") }}</p>
          <a class="box-span" target="_blank" href="/article/help-center">{{
            authStore.i18n("cm_news.helpCenter")
          }}</a>
          <a class="box-span" target="_blank" href="/article/quick-guide">{{
            authStore.i18n("cm_news.quickGuide")
          }}</a>
          <a class="box-span" target="_blank" href="/article/payment-methods">{{
            authStore.i18n("cm_news.paymentMethods")
          }}</a>
          <a class="box-span" target="_blank" href="/article/tutorials">{{
            authStore.i18n("cm_news.chilatshopTutorials")
          }}</a>
        </div>
      </div>
      <div class="icon-wrapper">
        <div class="pay-wrapper">
          <img
            loading="lazy"
            lazy
            class="img"
            v-for="(url, index) in payIcons"
            :key="index"
            :src="url"
            referrerpolicy="no-referrer"
          />
        </div>
        <div class="link-wrapper">
          <a
            v-for="(icon, index) in icons"
            :key="index"
            :href="icon.href"
            target="_blank"
          >
            <n-image
              lazy
              preview-disabled
              class="img"
              :src="icon.src"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
          </a>
        </div>
      </div>
      <div class="right-wrapper">
        2024 Chilat shop Todos los derechos reservados.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import facebookIcon from "@/assets/icons/facebook.png";
import inIcon from "@/assets/icons/in.png";
import insIcon from "@/assets/icons/ins.png";
import youtubeIcon from "@/assets/icons/youtube.png";
import tiktokIcon from "@/assets/icons/tiktok.png";
import videos from "@/assets/icons/videos.svg";
import { useAuthStore } from "@/stores/authStore";

const loginRegister = inject<any>("loginRegister");
const userInfo = computed(() => useAuthStore().getUserInfo);

const props = defineProps({
  footerWidth: {
    type: String,
    default: "1280px",
  },
});

const authStore = useAuthStore();
const pageWidth = ref(null);
const showFooter = ref(false);

const icons = [
  {
    href: "https://www.facebook.com/profile.php?id=61563281785563&mibextid=LQQJ4d",
    src: facebookIcon,
  },
  {
    href: "http://www.linkedin.com/in/chilat-shop-22904831a",
    src: inIcon,
  },
  {
    href: "https://www.instagram.com/chilatshop_oficial?igsh=MXB4ZWZlOG8wdGYyaQ==",
    src: insIcon,
  },
  {
    href: "https://www.youtube.com/@ChilatShop",
    src: youtubeIcon,
  },
  {
    href: "https://www.tiktok.com/@chilatshop?_t=8oJF69Z5Xlp&_r=1",
    src: tiktokIcon,
  },
  {
    href: "https://www.youtube.com/@Chilatshop-tutorial",
    src: videos,
  },
];

const payIcons = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/c3f4726c-72bb-4b6d-bf58-aa0e0019c85a.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/eccab92a-e50c-4fcb-8fe6-71c6272dff11.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/10df6bc2-6f1e-4d62-8f77-4e1938f0cb3f.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/82efd70a-9999-4a39-b379-0056244d56ed.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/fec58d91-daf4-443f-bbab-42986696609f.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1a429d67-f273-4956-a9e4-717b840885a1.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4c73c6b3-b79e-468b-a295-25c9256766e2.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/ca007192-e294-487a-825f-db9ce89141c5.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/d469d4cf-9e91-43e3-9d0c-8dcb0033b421.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/baadd159-c93b-478d-9a81-dbc8618553d7.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/762bf491-b80b-4eae-af50-4235305d12df.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/a54baa22-2353-45cc-9d20-3f97f1cffd57.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/1eb409a6-43bf-4c02-ac2e-0921b3018c41.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/89fe5271-3ae0-4acb-9c75-88228b6fd176.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4214386b-966b-4f73-9ad4-7ad3eeea5daf.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/4858c059-c506-4744-83e4-c86aa43de80b.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/f56d8c3f-becd-43c4-bc28-0b7b26631f62.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/79b18fa6-5a96-419c-85ec-3f72f3e4f71d.png",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/uat/2024/08/01/71271e5d-13a3-41c8-9075-23555141c480.png",
];

const onLoginClick = async (type: any) => {
  // 埋点
  if (type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_bottom_register",
      "点击底部栏目注册"
    );
  } else {
    window?.MyStat?.addPageEvent(
      "passport_open_bottom_login",
      "点击底部栏目登录"
    );
  }

  loginRegister?.openLogin("", type);
};

function onGoAccount(event: any) {
  navigateToPage(`/user/account`, {}, true, event);
}

onMounted(() => {
  showFooter.value = true;
});
</script>
<style scoped lang="scss">
.page-footer {
  width: max-content;
  min-width: 100%;
  background-color: #f8f9fa;
  border-top: 1px solid #e8e8e8;
  z-index: 1;
  display: flex;
  justify-content: center;
}
.footer-box {
  width: 100%;
  display: flex;
  padding: 60px 0 30px;
  justify-content: space-between;
  .box-wrapper {
    flex: 0 0 210px;
    display: flex;
    flex-direction: column;
    .box-title {
      color: #404040;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 30px;
      cursor: default;
      width: 120px;
      min-height: 48px;
    }
    .other-title {
      color: #404040;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 10px;
      cursor: default;
      width: 120px;
    }
    .box-span {
      font-size: 14px;
      color: #767676;
      margin-bottom: 15px;
      cursor: pointer;
      line-height: 1.5;
      display: inline-block;
    }
  }
  .box-wrapper:first-child {
    flex: 0 0 280px;
    margin-right: auto;
    display: block;
  }
}
.pay-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex: 0 0 460px;
  .img {
    height: 25px;
    margin-right: 10px;
    margin-bottom: 15px;
  }
}
.page-footer .icon-wrapper {
  width: 100%;
  display: flex;
  padding-top: 10px;
  padding-bottom: 35px;
  justify-content: space-between;
  .link-wrapper {
    display: flex;
    flex: 0 0 280px;
    .img {
      width: 26px;
      height: 26px;
      margin-left: 20px;
    }
  }
}
.right-wrapper {
  height: 60px;
  margin: 0 auto;
  border-top: 1px solid #e1e1e1;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
