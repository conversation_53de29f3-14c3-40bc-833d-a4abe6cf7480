syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "chilat/basis/model/goods_model.proto";
import "common.proto";
import "common/business.proto";

// 促销活动商品索引页返回
message MidPromotionGoodsIndexResp {
  common.Result result = 1;
  MidPromotionGoodsIndexModel data = 2;
}
// 促销活动商品索引页信息
message MidPromotionGoodsIndexModel {
  string id = 10; // 促销活动ID
  common.GoodsSelectorType selectorType = 110; // 商品筛选器类型
  repeated MidPromotionGoodsSelectorModel selectorList = 120; // 商品筛选器列表
}

// 促销活动商品的楼层信息
message MidPromotionGoodsSelectorModel {
  string selectorId = 10; // 商品筛选器ID（根据 PromotionGoodsIndexModel.selectorType 确定：营销规则ID or 商品标签ID）
  string selectorName = 20; // 商品筛选器名称
  string bannerUrl = 30; // 横幅广告图片URL（空值表示不显示banner图片）
  int32 goodsCount = 40; // 商品数量（检索到的商品总数）
  repeated chilat.basis.MidSearchGoodsModel goodsList = 50; //商品列表
}

// 获取商城短链接请求返回
message MidPromotionMallShortLinkResp {
  common.Result result = 1;
  MidPromotionMallShortLinkModel data = 2;
}

// 获取商城短链接请求结果
message MidPromotionMallShortLinkModel {
  string shortUrl = 10; //转换短链接后的URL
  string padc = 20; //促销活动动态代码（最新生成的padc）
  string entityDefaultPadc = 30; //活动主表存储的默认padc
  string entityLastPadc = 40; //活动主表存储的最后获取的padc
}
