syntax = "proto3";
package chilat.topic;

option java_package = "com.chilat.rpc.topic";

import "chilat/commodity/commodity_common.proto";

import "chilat/topic/model/goods_find_game_model.proto";
import "chilat/topic/param/goods_find_game_param.proto";
import "common.proto";

service GoodsFindGame {
  rpc save (GoodsFindGameSaveParam) returns (common.ApiResult);

  rpc list (common.EmptyParam) returns (GoodsFindGameListResp);

  //根据groupId查询寻货详情
  rpc detail (common.IdParam) returns (GoodsFindGameDetailResp);

  rpc exportExcel(common.EmptyParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
}