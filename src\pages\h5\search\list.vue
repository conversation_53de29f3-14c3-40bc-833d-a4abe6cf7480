<template>
  <div class="mobile-container flex flex-col" ref="containerRef">
    <seo-data :pageData="pageData"></seo-data>
    <!-- 头部信息 -->
    <div>
      <mobile-search-box
        :listType="pageData.listType"
        @onChangeListType="onChangeListType"
      ></mobile-search-box>
    </div>

    <!-- 非以图搜图搜索，展示营销分类，以及标签 -->
    <div
      v-if="!pageData.imageUrl"
      class="w-full bg-white z-1 px-[0.24rem]"
      id="affix-wrapper"
    >
      <div class="flex" id="affix-tab">
        <n-tabs
          animated
          ref="tabsRef"
          type="line"
          class="flex-1 px-[0.08rem]"
          :class="showMoreCate ? 'w-[95%]' : 'w-[100%]'"
          v-model:value="pageData.childCategoryId"
          :on-update:value="onCateClick"
        >
          <n-tab-pane
            v-for="cate in pageData?.filterCates"
            :key="cate.id"
            :tab="cate.name"
            :name="cate.id"
          ></n-tab-pane
        ></n-tabs>
        <div
          v-if="showMoreCate"
          @click="onShowMoreCate"
          class="basis-[0.6rem] flex-shrink-0 flex items-center justify-center"
        >
          <img
            loading="lazy"
            alt="arrow"
            src="@/assets/icons/arrowDown.svg"
            class="w-[0.24rem]"
            referrerpolicy="no-referrer"
          />
        </div>
      </div>
      <div class="py-[0.24rem]">
        <n-space>
          <n-button
            v-for="query in queryData"
            :key="query.key"
            :secondary="!isActiveQuery(query)"
            :ghost="isActiveQuery(query)"
            :color="isActiveQuery(query) ? '#e50113' : ''"
            class="!p-0 rounded-[0.08rem] h-[0.56rem]"
            @click="onQueryClick(query)"
          >
            <div class="px-[0.2rem] text-[0.28rem] leading-[0.28rem]">
              {{ query.value }}
            </div>
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- 以图搜图列表图片展示 -->
    <div
      v-if="pageData.imageUrl"
      class="flex justify-end p-[0.16rem] border-b-1 border-solid border-gray-200 bg-white w-full mb-[0.18rem]"
    >
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        :src="pageData.imageUrl"
        class="w-[0.64rem] h-[0.64rem] border border-[#e50113] block rounded-[0.06rem] search-image-wrapper"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
    </div>

    <div
      v-if="pageData.pageData?.goodsList?.length"
      id="affix-custom-search"
      class="relative h-[1rem] px-[0.2rem] bg-[#F4F9FE] inline-flex items-center rounded-tl-[0.24rem] rounded-tr-[0.24rem] overflow-hidden mx-[0.16rem]"
    >
      <div class="w-[2.8rem] text-[0.28rem] leading-[0.32rem] text-[#6C839A]">
        {{ authStore.i18n("cm_goods.needHelpSearching") }}
      </div>
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="w-[1.12rem] absolute bottom-[-0.42rem] left-[2rem]"
        src="@/assets/icons/common/find-blue.svg"
      />
      <a
        href="/h5/search/looking"
        data-spm-box="button-find-goods-list-above"
        class="py-[0.16rem] px-[0.2rem] text-center rounded-[10rem] text-[#fff] bg-[#11263B] inline-flex text-[0.28rem] leading-[0.28rem] hover:bg-[#007FFF] transition-all duration-300 cursor-pointer text-nowrap ml-auto"
      >
        {{ authStore.i18n("cm_goods.customSearch") }}
      </a>
    </div>

    <!-- 商品列表 -->
    <div
      class="relative w-full"
      id="drawer-target"
      :class="pageData.pageData?.goodsList?.length ? 'content-wrapper' : ''"
    >
      <div v-if="!pageData.loadingPage">
        <div
          v-if="pageData.pageData?.goodsList?.length"
          :data-spm-box="pageSource"
        >
          <div class="my-2 sticky z-auto">
            <div v-if="currentBanner">
              <img
                loading="lazy"
                class="carousel-img pb-1 w-full"
                :src="currentBanner"
                referrerpolicy="no-referrer"
              />
            </div>
          </div>
          <n-space vertical :style="{ gap: '0 0.04rem' }">
            <div v-if="pageData.listType === 'list'">
              <mobile-product-card
                v-for="(goods, index) in pageData.pageData?.goodsList"
                :goods="goods"
                :goodsIndex="index"
                :key="goods?.goodsId"
                :pageSource="pageSource"
                @onOpenDetail="onOpenDetail"
                @onUpdateGoodsId="onUpdateGoodsId"
                @onUpdateLoading="onUpdateLoading"
              />
            </div>
            <div class="px-[0.24rem]" v-else>
              <div class="flex flex-wrap justify-between">
                <mobile-product-grid
                  v-for="(goods, index) in pageData.pageData?.goodsList"
                  :key="index"
                  :goods="goods"
                  :goodsIndex="index"
                  :pageSource="pageSource"
                  @onOpenDetail="onOpenDetail"
                  @onUpdateGoodsId="onUpdateGoodsId"
                  @onUpdateLoading="onUpdateLoading"
                  class="mb-[0.4rem]"
                />
              </div>
            </div>
          </n-space>
          <div>
            <img
              loading="lazy"
              v-if="pageData.isLoading"
              src="@/assets/icons/loading.svg"
              class="w-[1rem] h-[1rem] mx-auto"
              referrerpolicy="no-referrer"
            />
          </div>
        </div>
        <div v-else class="pt-[0.2rem] pb-[0.28rem] px-[0.24rem]">
          <div class="flex flex-col items-center gap-[0.2rem]">
            <img
              loading="lazy"
              src="@/assets/icons/common/no-data.svg"
              class="w-[3.8rem]"
            />
            <div
              class="w-full px-[0.16rem] text-[0.32rem] leading-[0.56rem] text-[#7F7F7F] text-center"
            >
              {{
                pageData.padc
                  ? authStore.i18n("cm_search.activityExpired")
                  : authStore.i18n("cm_search.noData")
              }}
            </div>
          </div>

          <a
            href="/h5/search/looking"
            data-spm-box="button-find-filter-empty"
            class="fixed bottom-[0.6rem] z-[101] cursor-pointer"
          >
            <img
              :alt="authStore.i18n('cm_goods.customSearch')"
              class="w-[1.6rem] absolute top-[-0.9rem] left-[0.32rem]"
              src="@/assets/icons/common/find-red.svg"
            />
            <div
              class="py-[0.32rem] px-[1.76rem] bg-[#11263B] rounded-[10rem] hover:bg-[#0F2E4D] transition-all duration-300 text-white text-[0.32rem] leading-[0.32rem]"
            >
              {{ authStore.i18n("cm_goods.customSearch") }}
            </div>
            <img
              :alt="authStore.i18n('cm_goods.customSearch')"
              class="w-[1.32rem] mt-[0.32rem] mx-auto"
              src="@/assets/icons/common/logo.png"
            />
          </a>
        </div>
      </div>
      <div v-else class="flex justify-center items-center mt-[30%]">
        <n-spin stroke="#e50113" :show="pageData.loadingPage"> </n-spin>
      </div>
    </div>

    <div>
      <div v-show="pageData.showLoading" class="loading-overlay">
        <n-spin stroke="#e50113" :show="pageData.showLoading"> </n-spin>
      </div>
      <div
        v-if="pageData.noMoreGoods && pageData.pageData?.goodsList?.length"
        class="text-[#7f7f7f] text-center text-[0.28rem] leading-[0.28rem] absolute bottom-[0.12rem] left-[50%] translate-x-[-50%]"
      >
        {{ authStore.i18n("cm_goods.noMoreGoods") }}
      </div>
    </div>

    <!-- 商品规格 -->
    <goods-spec
      :visible="pageData.visible"
      :goods="pageData.currentGoods"
      @onCloseDetail="onCloseDetail"
      @onUpdateList="onUpdateGoods"
    ></goods-spec>

    <!-- 营销分类子分类显示 -->
    <n-drawer
      v-model:show="pageData.showCate"
      :height="200"
      placement="top"
      :trap-focus="false"
      :block-scroll="true"
      to="#drawer-target"
      class="custom-drawer"
      style="
        position: fixed;
        top: 2.56rem;
        left: 50%;
        transform: translateX(-50%);
        width: 101%;
      "
      :style="{ top: pageData.bottomOffset }"
    >
      <n-drawer-content class="flex">
        <n-grid :cols="2" x-gap="12" y-gap="18">
          <n-grid-item
            @click="onSubCateClick(cate)"
            v-for="(cate, index) in pageData?.chooseCategory?.children"
            :key="index"
          >
            <div
              class="min-h-[0.6rem] text-[0.28rem] leading-[0.32rem] hover:text-[#E50113] pb-[0.16rem] hover:border-b-2 hover:border-[#E50113]"
            >
              {{ cate.name }}
            </div>
          </n-grid-item>
        </n-grid>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
import _, { size } from "lodash";
import { useAuthStore } from "@/stores/authStore";
import mercadoHotBanner from "@/assets/icons/mercadoHotH5Banner.jpg";
import yiwuHotBanner from "@/assets/icons/yiwuHotH5Banner.jpg";
import backSchoolHotBanner from "@/assets/icons/backSchoolHotBanner.jpg";
import winterHotSaleBanner from "@/assets/icons/goods/winter_hot_sale_banner.jpg";

import MobileSearchBox from "@/pages/h5/search/components/MobileSearchBox.vue";
import MobileProductCard from "@/pages/h5/search/components/MobileProductCard.vue";
import MobileProductGrid from "@/pages/h5/search/components/MobileProductGrid.vue";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const containerRef = ref<HTMLElement | undefined>(undefined);
const tabsRef = ref<any>(null);
const pageData = reactive(<any>{
  goodsName: "",
  keyword: route.query.keyword || "",
  cateName: route?.query?.cateName || "",
  categoryId: route?.query?.categoryId || route?.query?.cateId || "0",
  type: route?.query?.type || "",
  imageUrl: route?.query?.imageUrl || "",
  imageId: route?.query?.imageId || "",
  tagIds: route?.query?.tagId
    ? [route?.query?.tagId]
    : route?.query?.type === "recommendSearch"
    ? [10000100, 10000200]
    : undefined, //推荐商品列表固定请求义乌热卖，美克多的数据
  marketingRuleId: route?.query?.marketingRuleId || "",
  padc: route?.query?.padc || "",
  minPrice: null as number | null,
  maxPrice: null as number | null,
  leMinBuyQuantity: null as number | null,
  isLoading: false,
  pricePair: [],
  filterCates: <any>[],
  loadingPage: true,
  showLoading: false,
  pageInfo: {
    current: 0,
    size: 30,
  },
  listType: "grid",
  chooseCategory: <any>{},
  childCategoryId: route?.query?.childCategoryId || null,
  showMoreCate: false,
  showCate: false,
  activeQueries: [],
  noMoreGoods: false,
});

const queryData = [
  {
    key: "addToCartCount",
    value: authStore.i18n("cm_goods.sortByPopular"),
  },
];

const pageSource = computed(() => {
  if (route?.query?.activityId || route?.query?.padc) {
    return "activity-goods-list";
  } else if (route?.query?.tagId === "10000100") {
    return "tag-goods-mercado";
  } else if (route?.query?.tagId === "10000200") {
    return "tag-goods-yiwu";
  } else if (route?.query?.tagId === "20001600") {
    return "tag-goods-school";
  } else if (route?.query?.tagId === "20000100") {
    return "tag-goods-camera";
  } else if (route?.query?.tagId === "20001100") {
    return "tag-goods-humidifier";
  } else if (route?.query?.tagId === "20003800") {
    return "tag-goods-packing";
  } else if (route?.query?.tagId === "20004300") {
    return "tag-goods-bestselling";
  } else if (route?.query?.tagId) {
    return "tag-goods-list ";
  } else if (route?.query?.type === "recommendSearch") {
    return "recommend-goods-list";
  } else if (route?.query?.keyword) {
    return "search-goods-list";
  } else if (route?.query?.cateName) {
    return "category-goods-list";
  } else if (route?.query?.imageUrl) {
    return "image-search-list";
  }
  return "goods-list";
});
const showMoreCate = computed(() => {
  return pageData.chooseCategory?.children?.length > 0;
});
const isActiveQuery = computed(() => {
  return (query: any) => pageData.activeQueries.includes(query.key);
});

const bannerConfig = {
  "10000100": mercadoHotBanner,
  "10000200": yiwuHotBanner,
  "20001600": backSchoolHotBanner,
  "20004300": winterHotSaleBanner,
} as const;

const currentBanner = computed(() => {
  const tagId = route?.query?.tagId as keyof typeof bannerConfig;
  return bannerConfig[tagId];
});

onBeforeMount(() => {
  onPageData();
});

onMounted(() => {
  pageData.childCategoryId && onUpdateTabLeft(pageData.childCategoryId);
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

async function onPageData() {
  pageData.loadingPage = true;
  const res: any = await useGoodsListPage({
    categoryId: pageData.categoryId,
    keyword: pageData.keyword,
    pageNo: pageData.pageInfo.current,
    pageSize: pageData.pageInfo.size,
    imageId: pageData.imageId,
    tagIds: pageData.tagIds,
    childCategoryId: pageData.childCategoryId ? pageData.childCategoryId : null,
    marketingRuleId: pageData.marketingRuleId,
    padc: pageData.padc,
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    authStore.getCartList(res?.data.cartInfo);
    if (
      !pageData?.pageData?.goodsList?.length &&
      pageData.categoryId &&
      pageData.categoryId != 0
    ) {
      const enabled = await onCheckCategoryStatus();
      if (!enabled) {
        return navigateTo("/h5/category");
      }
    }

    // 未找到商品，则跳转找货列表
    if (
      !pageData.pageData?.goodsList?.length &&
      !pageData.minPrice &&
      pageData.minPrice != 0 &&
      !pageData.maxPrice &&
      pageData.maxPrice != 0 &&
      !pageData.leMinBuyQuantity &&
      !pageData.padc
    ) {
      router.push({
        path: "/h5/search/looking",
        query: {
          keyword: pageData.keyword,
          cateName: pageData.cateName,
          imageUrl: pageData.imageUrl,
        },
      });
      return;
    }

    onMarkCartItems(pageData.pageData?.goodsList, pageData.cartInfo?.goodsList);
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
    pageData.pageInfo = res?.data?.pageData?.page;
    onGetMarketCate();
  }
  pageData.loadingPage = false;
}

async function onCheckCategoryStatus() {
  const res: any = await useGetCategoryInfo({
    id: pageData.categoryId,
  });
  if (res?.result?.code === 200) {
    return res?.data?.enabled;
  }
  return false;
}

// 检查当前商品是否在购物车中 `goodsId` 不能唯一标识商品时，需额外比较 `padc` 字段
function onMarkCartItems(goodsList: any[], cartList: any[]) {
  if (!goodsList || !cartList) return;
  goodsList?.forEach((goods) => {
    const isInCart = cartList?.some((cart) => {
      if (cart.goodsId !== goods.goodsId) return false;
      if ("padc" in cart || "padc" in goods) {
        return cart?.padc === goods?.padc;
      }
      return true;
    });
    if (isInCart) {
      goods.selected = true;
    }
  });
}

// 获取营销分类
async function onGetMarketCate() {
  pageData.filterCates = _.cloneDeep(pageData?.pageData?.categoryFilters ?? []);
  pageData.filterCates.unshift({
    id: 0,
    name: authStore.i18n("cm_goods.all"),
    children: _.cloneDeep(pageData?.pageData?.categoryFilters),
  });
  pageData.childCategoryId = pageData.filterCates.some(
    (item: any) => item.id === pageData.childCategoryId
  )
    ? pageData.childCategoryId
    : 0;
  pageData.chooseCategory = pageData.filterCates.find(
    (item: any) => item.id === pageData.childCategoryId
  );
}

// 加载下一页
async function onScrollBottom(e: any) {
  if (pageData.pageData?.goodsList?.length === 0) return;
  const affixElement = document.getElementById("affix-wrapper") || <any>{};
  const affixCustomSearch =
    document.getElementById("affix-custom-search") || <any>{};

  if (!isEmptyObject(affixElement)) {
    //吸顶
    if (affixElement.offsetTop < window.scrollY) {
      affixElement.style.position = "fixed";
      affixElement.style.top = 0;

      // 当affix-wrapper固钉时，affix-custom-search也固钉，位置在affix-wrapper后面
      if (!isEmptyObject(affixCustomSearch)) {
        affixCustomSearch.style.position = "fixed";
        affixCustomSearch.style.top = affixElement.offsetHeight - 1 + "px";
        affixCustomSearch.style.left = 0;
        affixCustomSearch.style.right = 0;
        affixCustomSearch.style.zIndex = "1";
        affixCustomSearch.style.width = "100%";
        affixCustomSearch.classList.remove(
          "mx-[0.16rem]",
          "rounded-tl-[0.24rem]",
          "rounded-tr-[0.24rem]"
        );
        affixCustomSearch.classList.remove("px-[0.2rem]");
        affixCustomSearch.classList.add("px-[0.24rem]");
      }
    } else {
      affixElement.style.position = "relative";
      affixElement.style.top = "";

      // 恢复affix-custom-search的样式
      if (affixCustomSearch) {
        affixCustomSearch.style.position = "";
        affixCustomSearch.style.top = "";
        affixCustomSearch.style.left = "";
        affixCustomSearch.style.right = "";
        affixCustomSearch.style.zIndex = "";
        affixCustomSearch.style.width = "";
        // 恢复圆角和边距
        affixCustomSearch.classList.add(
          "mx-[0.16rem]",
          "rounded-tl-[0.24rem]",
          "rounded-tr-[0.24rem]"
        );
        affixCustomSearch.classList.remove("px-[0.24rem]");
        affixCustomSearch.classList.add("px-[0.2rem]");
      }
    }
  } else if (affixCustomSearch) {
    // 以图搜图搜索时，没有affix-wrapper，affix-custom-search直接固钉顶部
    if (window.scrollY > 0) {
      affixCustomSearch.style.position = "fixed";
      affixCustomSearch.style.top = 0;
      affixCustomSearch.style.left = 0;
      affixCustomSearch.style.right = 0;
      affixCustomSearch.style.zIndex = "1";
      affixCustomSearch.style.width = "100%";
      // 移除圆角和边距
      affixCustomSearch.classList.remove(
        "mx-[0.16rem]",
        "rounded-tl-[0.24rem]",
        "rounded-tr-[0.24rem]"
      );
      affixCustomSearch.classList.remove("px-[0.2rem]");
      affixCustomSearch.classList.add("px-[0.24rem]");
    } else {
      affixCustomSearch.style.position = "";
      affixCustomSearch.style.top = "";
      affixCustomSearch.style.left = "";
      affixCustomSearch.style.right = "";
      affixCustomSearch.style.zIndex = "";
      affixCustomSearch.style.width = "";
      // 恢复圆角和边距
      affixCustomSearch.classList.add(
        "mx-[0.16rem]",
        "rounded-tl-[0.24rem]",
        "rounded-tr-[0.24rem]"
      );
    }
  }

  if (pageData.isLoading || pageData.noMoreGoods) return;
  // 判断是否滚动到底部
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return;
  pageData.isLoading = true;
  onGetGoodsListData("scroll");
}

async function onGetGoodsListData(scroll?: any) {
  if (scroll && pageData.noMoreGoods) return;
  if (scroll) {
    pageData.pageInfo.current++;
    window?.MyStat.addPageEvent(
      "page_load",
      `加载页数：${pageData.pageInfo.current}`
    ); // 埋点
  } else {
    pageData.loadingPage = true;
    pageData.noMoreGoods = false;
    pageData.pageInfo.current = 1;
    pageData.pageData.goodsList = [];
  }
  const minPrice = pageData.pricePair[0];
  const maxPrice = pageData.pricePair[1];
  const params = <any>{
    keyword: pageData.keyword,
    categoryId: pageData.categoryId,
    pageNo: pageData.pageInfo.current,
    pageSize: pageData.pageInfo.size,
    // minPrice,
    // maxPrice,
    // leMinBuyQuantity: pageData.leMinBuyQuantity,
    childCategoryId: pageData.childCategoryId ? pageData.childCategoryId : null,
    imageId: pageData.imageId,
    tagIds: pageData.tagIds,
    marketingRuleId: pageData.marketingRuleId,
    padc: pageData.padc,
  };
  if (pageData.activeQueries.includes("addToCartCount")) {
    params.sortField = 42;
  }
  try {
    const res: any = await useGoodsPageListData(params);
    pageData.loadingPage = false;
    if (scroll && (!res.data || !res.data?.goodsList?.length)) {
      pageData.noMoreGoods = true;
      pageData.isLoading = false;
      return;
    }
    onMarkCartItems(res.data?.goodsList, authStore.$state.cartList);
    if (scroll) {
      pageData.pageData.goodsList = pageData.pageData.goodsList.concat(
        res.data?.goodsList
      );
      // 以图搜图不做去重处理
      if (pageData.type !== "imgSearch") {
        pageData.pageData.goodsList = pageData.pageData.goodsList.reduce(
          (prev: any, current: any) => {
            if (!prev.some((item: any) => item.goodsId === current.goodsId)) {
              prev.push(current);
            }
            return prev;
          },
          []
        );
      }
      pageData.pageInfo = res?.data?.page;
    } else {
      pageData.pageData.goodsList = res.data?.goodsList || [];
      if (pageData.pageData.goodsList.length) {
        pageData.pageInfo = res?.data?.page;
      }
      // 查不到商品 跳转至找货列表
      if (
        !pageData.pageData.goodsList.length &&
        !minPrice &&
        minPrice != 0 &&
        !maxPrice &&
        maxPrice != 0 &&
        !pageData.leMinBuyQuantity &&
        !pageData.padc
      ) {
        window.location.href = `/h5/search/looking?keyword=${pageData.keyword}`;
        return;
      }
    }
  } catch (error) {
    --pageData.pageInfo.current;
  } finally {
    pageData.isLoading = false;
  }
}

async function getGoodsId(goods: any) {
  const res: any = await useGetGoods({ str: goods.sourceGoodsId });
  if (res?.result?.code === 200) {
    return res?.data;
  } else {
    showToast(authStore.i18n("cm_common_addGoodsError"));
  }
  return null;
}

async function onOpenDetail(e: any, goodsIndex: any) {
  pageData.showLoading = true;
  const goods = pageData.pageData.goodsList[goodsIndex];
  if (!goods.goodsId) {
    goods.goodsId = await getGoodsId(goods);
    if (!goods.goodsId) {
      pageData.showLoading = false;
      return;
    }
    pageData.pageData.goodsList[goodsIndex].goodsId = goods.goodsId;
  }

  pageData.visible = true;
  goods.spm = window.MyStat.getPageSPM(e);
  pageData.currentGoods = goods;
  pageData.showLoading = false;
}

function onUpdateGoodsId(goodsIndex: any, goodsId: any) {
  pageData.pageData.goodsList[goodsIndex].goodsId = goodsId;
}

function onUpdateLoading(val: any) {
  pageData.showLoading = val;
}

function onCloseDetail() {
  pageData.visible = false;
}

// 商品加购后 更新商品列表的选中状态
async function onUpdateGoods(goodsId: any) {
  pageData.pageData.goodsList.map((item: any) => {
    if (item.goodsId === goodsId) {
      item.selected = true;
    }
  });
}

// 商品列表布局切换 list/grid
function onChangeListType(val: any) {
  pageData.listType = val;
}

// 营销分类点击
function onCateClick(cateId: any) {
  onUpdateCateId(cateId);
  onUpdateTabLeft(cateId);
  pageData.showCate = false;
}

// 营销子分类点击
function onSubCateClick(cate: any) {
  pageData.showCate = false;
  // 点击todos下的分类时，页面不会刷新，切换至对应的分类
  if (pageData.childCategoryId === 0) {
    onUpdateCateId(cate.id);
    onUpdateTabLeft(cate.id);
  } else {
    navigateToPage(
      "/h5/search/list",
      {
        categoryId: pageData.chooseCategory.id,
        cateName: pageData.chooseCategory.name,
        childCategoryId: cate.id,
        childCateName: cate.name,
        ...(route.query.type === "recommendSearch" && {
          type: "recommendSearch",
        }),
        ...(route.query.tagId && {
          tagId: route.query.tagId,
          tag: route.query.tag,
        }),
      },
      false
    );
  }
}

function onUpdateTabLeft(cateId?: any) {
  nextTick(() => {
    const selectedTab = tabsRef?.value?.$el?.querySelector(
      `[data-name="${cateId}"]`
    ); // 当前选中的 tab
    const tabsContainer = tabsRef.value.$el.querySelector(".v-x-scroll"); // 滚动容器
    if (tabsContainer && selectedTab) {
      const containerWidth = tabsContainer.offsetWidth;
      const tabLeft = selectedTab.offsetLeft;
      const tabWidth = selectedTab.offsetWidth;
      const targetScrollLeft = tabLeft - containerWidth / 2 + tabWidth / 2;
      // 平滑滚动
      tabsContainer.scrollTo({
        left: targetScrollLeft,
        behavior: "smooth",
      });
    }
  });
}

// 更新选中的营销分类 ID、商品列表和路由地址参数
async function onUpdateCateId(cateId?: any) {
  pageData.childCategoryId = cateId;
  pageData.chooseCategory = pageData?.filterCates.find((item: any) => {
    return item.id == pageData.childCategoryId;
  });
  onGetGoodsListData(0);
  const url = new URL(window.location.href);
  url.searchParams.set("childCategoryId", cateId);
  url.searchParams.set("childCateName", pageData.chooseCategory.name);
  window.history.replaceState(null, "", url.toString());
}

// 展示子分类
function onShowMoreCate() {
  pageData.showCate = !pageData.showCate;
  if (pageData.showCate) {
    const affixElement = document.getElementById("affix-tab");
    if (affixElement) {
      const rect = affixElement.getBoundingClientRect();
      pageData.bottomOffset = rect.bottom + "px";
    }
  }
}

// 筛选项点击
function onQueryClick(query: any) {
  const index = pageData.activeQueries.indexOf(query.key);
  if (index > -1) {
    pageData.activeQueries.splice(index, 1);
  } else {
    pageData.activeQueries.push(query.key);
  }
  onGetGoodsListData();
}
</script>

<style scoped lang="scss">
.mobile-container {
  height: 100%;
  min-height: 100vh;
  padding-bottom: 0.32rem;
  overflow-y: auto;
  overflow-x: hidden;
}

:deep(.n-empty__description) {
  max-width: 80%;
  color: #333;
  text-align: center;
}

:deep(.search-image-wrapper) {
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}

.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.n-tabs-pane-wrapper) {
  display: none;
}
:deep(.n-tabs.n-tabs--top .n-tabs-nav-scroll-wrapper::after) {
  box-shadow: none;
}
:deep(.n-tabs.n-tabs--top .n-tabs-nav-scroll-wrapper::before) {
  box-shadow: none;
}
:deep(.v-x-scroll) {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.custom-drawer {
  transition: none;
}
:deep(.n-drawer-mask) {
  position: fixed;
  top: 3.2rem;
}
:deep(#affix-tab .n-tabs .n-tabs-tab .n-tabs-tab__label) {
  font-size: 0.28rem;
}

.content-wrapper {
  box-shadow: 0 -0.04rem 0.16rem 0 rgba(16, 38, 59, 0.02);
}
</style>
